from django.contrib import admin
from .models import Company, Role, Permission, User, Notification, Order, OrderItem, Country
from django.contrib.auth.admin import UserAdmin

admin.site.register(Company)
admin.site.register(Role)
admin.site.register(Permission)
admin.site.register(Notification)
admin.site.register(Order)
admin.site.register(OrderItem)
admin.site.register(Country)




class UserAdmin(UserAdmin):
    list_display = ('id', 'email', 'first_name', 'last_name', 'is_staff')
    search_fields = ('email', 'first_name', 'last_name')
    readonly_fields = ('uuid','updated_at','created_at')

    filter_horizontal = ()
    list_filter = ()
    fieldsets = (
        (None, {
            'fields': (
                'password', 'last_login', 'email', 
                'phone', 'first_name', 'last_name', 'username',
                'created_at', 'uuid',
            )
        }),
        (('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser',
                                       'groups', 'user_permissions')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'is_staff', 'is_superuser')
        }),
    )

    ordering = ('email',)
    filter_horizontal = ()
    list_filter = ()
    fieldsets = ()

admin.site.register(User, UserAdmin)

