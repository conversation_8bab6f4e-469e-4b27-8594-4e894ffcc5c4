from django.urls import reverse
from rest_framework import status
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils.encoding import smart_bytes
from django.utils.http import urlsafe_base64_encode
from common.models import User, Company, ProductReview
from test_base.base import BaseAPITestCase
from django.urls import reverse
from rest_framework import status
from test_base.base import BaseAPITestCase
from test_base.factories import (
    CompanyFactory, SellerCompanyFactory, ProductFactory, 
    ProductVariantFactory, BuyerCompanyFactory, BuyerCompanyEmployeeFactory,
    ProductReviewFactory, BuyerSellerRelationshipFactory, ProductCategoryFactory,
    ProductVisibilityRuleFactory, WarehouseFactory, WarehouseInventoryFactory
)

class CommonAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(
            name="Test Company",
            organisation_number="12345",
            street_address="Test Street",
            zip_code="12345",
            city="Test City",
            contact_phone_number="1234567890",
            contact_email="<EMAIL>"
        )

        self.buyer_company = BuyerCompanyFactory(company=self.company, credit_limit=1000)
        self.buyer_employee = BuyerCompanyEmployeeFactory(
            buyer_company=self.buyer_company,
            user=self.user,
        )

    def test_login(self):
        url = reverse('token_obtain_pair')
        data = {'email': self.user.email, 'password': 'testpassword'}
        response = self.api_post(url, data)
        self.assertSuccessResponse(response)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_login_invalid_credentials(self):
        url = reverse('token_obtain_pair')
        data = {'email': self.user.email, 'password': 'wrongpassword'}
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_401_UNAUTHORIZED)

    def test_logout(self):
        url = reverse('logout')
        response = self.api_post(url)
        self.assertSuccessResponse(response)

    def test_refresh_token(self):
        login_url = reverse('token_obtain_pair')
        login_data = {'email': self.user.email, 'password': 'testpassword'}
        login_response = self.api_post(login_url, login_data)
        
        refresh_url = reverse('token_refresh')
        refresh_data = {'refresh': login_response.data['refresh']}
        response = self.api_post(refresh_url, refresh_data)
        self.assertSuccessResponse(response)
        self.assertIn('access', response.data)

    def test_forgot_password(self):
        url = reverse('forgotpassword')
        data = {'email': self.user.email}
        response = self.api_post(url, data)
        self.assertSuccessResponse(response)

    def test_forgot_password_invalid_email(self):
        url = reverse('forgotpassword')
        data = {'email': '<EMAIL>'}
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)

    def test_reset_password(self):
        uidb64 = urlsafe_base64_encode(smart_bytes(self.user.pk))
        token = PasswordResetTokenGenerator().make_token(self.user)
        url = reverse('resetpassword')
        data = {
            'new_password1': 'newpassword123',
            'new_password2': 'newpassword123',
            'uidb64': uidb64,
            'token': token
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response)

    def test_reset_password_mismatch(self):
        uidb64 = urlsafe_base64_encode(smart_bytes(self.user.pk))
        token = PasswordResetTokenGenerator().make_token(self.user)
        url = reverse('resetpassword')
        data = {
            'new_password1': 'newpassword123',
            'new_password2': 'differentpassword',
            'uidb64': uidb64,
            'token': token
        }
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)




class PublicProductViewSetTestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.seller_company = SellerCompanyFactory(webshop_is_public=True, domain="test-seller.com")
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.product = ProductFactory(seller_company=self.seller_company, is_active=True)
        self.variant = ProductVariantFactory(product=self.product, is_active=True)
        self.category = ProductCategoryFactory(seller_company=self.seller_company)
        
        self.buyer_company = BuyerCompanyFactory()
        self.buyer_employee = BuyerCompanyEmployeeFactory(buyer_company=self.buyer_company, user=self.user)
        
        # Add warehouse inventory
        WarehouseInventoryFactory(warehouse=self.warehouse, product_variant=self.variant, quantity=10)

    def test_list_public_products_no_rules(self):
        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], self.product.id)

    def test_retrieve_public_product_no_rules(self):
        url = reverse('public-products-detail', kwargs={'uuid': self.product.uuid})
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.product.id)
        self.assertEqual(len(response.data['variants']), 1)

    def test_list_products_with_visibility_rule(self):
        product2 = ProductFactory(seller_company=self.seller_company, is_active=True)
        ProductVisibilityRuleFactory(
            seller_company=self.seller_company,
            buyer_company=self.buyer_company,
            visibility_type='PRODUCT',
            product=product2
        )

        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 2)  # Both products visible

        # Test with a different user who shouldn't see the product with the rule
        other_user = BuyerCompanyEmployeeFactory().user
        self.client.force_authenticate(user=other_user)
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)  # Only the product without rule is visible

    def test_list_products_with_variant_visibility_rule(self):
        ProductVisibilityRuleFactory(
            seller_company=self.seller_company,
            buyer_company=self.buyer_company,
            visibility_type='VARIANT',
            product_variant=self.variant
        )

        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)  # Product is visible due to variant rule

        # Test with a different user who shouldn't see the product
        other_user = BuyerCompanyEmployeeFactory().user
        self.client.force_authenticate(user=other_user)
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)  # No products visible

    def test_list_products_with_category_visibility_rule(self):
        self.product.category = self.category
        self.product.save()

        ProductVisibilityRuleFactory(
            seller_company=self.seller_company,
            buyer_company=self.buyer_company,
            visibility_type='CATEGORY',
            product_category=self.category
        )

        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)  # Product is visible due to category rule

        # Test with a different user who shouldn't see the product
        other_user = BuyerCompanyEmployeeFactory().user
        self.client.force_authenticate(user=other_user)
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)  # No products visible

    def test_list_private_products_without_access(self):
        self.seller_company.webshop_is_public = False
        self.seller_company.save()
        
        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertErrorResponse(response, status.HTTP_403_FORBIDDEN)

    def test_list_private_products_with_access(self):
        self.seller_company.webshop_is_public = False
        self.seller_company.save()
        BuyerSellerRelationshipFactory(buyer_company=self.buyer_company, seller_company=self.seller_company, is_active=True)

        url = reverse('public-products-list')
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_list_products_without_domain(self):
        url = reverse('public-products-list')
        response = self.api_get(url)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)

    def test_product_with_warehouse_inventory(self):
        url = reverse('public-products-detail', kwargs={'uuid': self.product.uuid})
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.product.id)
        self.assertEqual(len(response.data['variants']), 1)

    def test_product_without_warehouse_inventory(self):
        new_product = ProductFactory(seller_company=self.seller_company, is_active=True)
        url = reverse('public-products-detail', kwargs={'uuid': new_product.uuid})
        response = self.api_get(f"{url}?domain={self.seller_company.domain}")
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], new_product.id)
        self.assertEqual(len(response.data['variants']), 0)

class ProductReviewViewSetTestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.seller_company = SellerCompanyFactory()
        self.product = ProductFactory(seller_company=self.seller_company)
        self.variant = ProductVariantFactory(product=self.product)
        
        self.buyer_company = BuyerCompanyFactory()
        self.buyer_employee = BuyerCompanyEmployeeFactory(buyer_company=self.buyer_company, user=self.user)
        
        self.review = ProductReviewFactory(product=self.product, buyer=self.buyer_employee)

    def test_list_reviews(self):
        url = reverse('productreview-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_create_review(self):
        url = reverse('productreview-list')
        data = {
            'product': self.product.id,
            'rating': 4,
            'comment': 'Great product!'
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(ProductReview.objects.count(), 2)

    def test_my_reviews(self):
        url = reverse('productreview-my-reviews')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_product_reviews(self):
        url = reverse('productreview-product-reviews')
        response = self.api_get(f"{url}?product_id={self.product.id}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_product_variant_reviews(self):
        ProductReviewFactory(product_variant=self.variant, buyer=self.buyer_employee)
        url = reverse('productreview-product-variant-reviews')
        response = self.api_get(f"{url}?variant_id={self.variant.id}")
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_create_review_invalid_data(self):
        url = reverse('productreview-list')
        data = {
            'product': self.product.id,
            'product_variant': self.variant.id,  # Both product and variant provided
            'rating': 4,
            'comment': 'Great product!'
        }
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)

    def test_create_review_no_product_or_variant(self):
        url = reverse('productreview-list')
        data = {
            'rating': 4,
            'comment': 'Great product!'
        }
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)