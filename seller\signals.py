from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import SellerCompanySetting, SellerCompany, PriceList
from common.models import Permission, Role
from django.contrib.contenttypes.models import ContentType
from common.enums import CompanyTypes


@receiver(post_save, sender=SellerCompany)
def seller_company_setup(sender, instance, created, **kwargs):
    if created:
        SellerCompanySetting.objects.create(seller_company=instance)

        # Create an "Administrator" role with all permissions related to company_type SELLER
        admin_role = Role.objects.create(
            name="Administratör",
            content_type=ContentType.objects.get_for_model(instance),
            object_id=instance.id
        )
        seller_permissions = Permission.objects.filter(company_types__icontains=CompanyTypes.SELLER.name)
        admin_role.permissions.set(seller_permissions)
        PriceList.objects.create(seller_company=instance, name="Standard", is_default=True)

