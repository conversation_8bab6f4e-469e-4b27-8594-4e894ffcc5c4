# seller/utils.py

from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from .models import DiscountAssignment, DiscountCode
from seller.models import ProductVariant
from common.enums import DiscountTypes
from decimal import Decimal

def calculate_discounted_price(base_price, product_or_variant, customer=None, quantity=1, discount_code=None):
    original_price = Decimal(base_price)
    current_price = original_price
    total_discount = Decimal('0.00')
    applicable_discounts = []
    # Get product/variant discounts    
    product_assignments = DiscountAssignment.objects.filter(
        content_type=ContentType.objects.get_for_model(product_or_variant),
        object_id=product_or_variant.id
    ).select_related('discount')

    # If it's a product variant and there are no direct assignments to the variant, check for discounts assignments on the parent product
    if not product_assignments and isinstance(product_or_variant, ProductVariant):
        product_assignments = DiscountAssignment.objects.filter(
            content_type=ContentType.objects.get_for_model(product_or_variant.product),
            object_id=product_or_variant.product.id
        ).select_related('discount')

    for assignment in product_assignments:
        if assignment.discount.is_valid():
            applicable_discounts.append(assignment.discount)

    # Get customer discounts
    if customer:
        customer_assignments = DiscountAssignment.objects.filter(
            content_type=ContentType.objects.get_for_model(customer),
            object_id=customer.id
        ).select_related('discount')
        for assignment in customer_assignments:
            if assignment.discount.is_valid():
                applicable_discounts.append(assignment.discount)

    # Apply discount code if provided
    if discount_code:
        try:
            code = DiscountCode.objects.get(code=discount_code) # TODO: must be filtered on seller_company
            if code.is_valid():
                applicable_discounts.append(code.discount)
        except DiscountCode.DoesNotExist:
            pass

    # Apply discounts
    combinable_discounts = [d for d in applicable_discounts if d.can_be_combined]
    non_combinable_discounts = [d for d in applicable_discounts if not d.can_be_combined]

    # Apply combinable discounts
    for discount in combinable_discounts:
        if discount.discount_type == DiscountTypes.PERCENTAGE.name:
            discount_amount = current_price * (discount.value / Decimal('100'))
            current_price -= discount_amount
            total_discount += discount_amount
        elif discount.discount_type == DiscountTypes.AMOUNT.name:
            discount_amount = min(discount.value, current_price)
            current_price = max(current_price - discount_amount, Decimal('0'))
            total_discount += discount_amount

    # Apply the best non-combinable discount if any
    if non_combinable_discounts:
        best_discount = max(non_combinable_discounts, key=lambda d: d.value if d.discount_type == DiscountTypes.AMOUNT.name else current_price * d.value / 100)
        if best_discount.discount_type == DiscountTypes.PERCENTAGE.name:
            discount_amount = current_price * (best_discount.value / Decimal('100'))
        else:
            discount_amount = min(best_discount.value, current_price)
        
        non_combinable_price = max(current_price - discount_amount, Decimal('0'))
        if non_combinable_price < current_price:
            total_discount += current_price - non_combinable_price
            current_price = non_combinable_price

    # Calculate discount percentage
    if original_price > 0:
        discount_percentage = (total_discount / original_price) * Decimal('100')
    else:
        discount_percentage = Decimal('0')

    return current_price, discount_percentage.quantize(Decimal('0.01'))