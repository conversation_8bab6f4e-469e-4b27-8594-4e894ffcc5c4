from .models import B<PERSON><PERSON><PERSON>, Blog<PERSON>ategory, FAQ, FAQCategory, Guide, Testimonial, BlogComments, HelpText, InformationText, VideoGuide
from rest_framework import serializers 
from bs4 import BeautifulSoup
import bleach
from django.conf import settings
from urllib.parse import urljoin

def get_full_media_url(relative_url):
    if relative_url:
        return urljoin(settings.BE_BASE_URL, relative_url)

class FAQCategorySerializer(serializers.ModelSerializer): 
    class Meta: 
        model = FAQCategory
        exclude = ['id', 'uuid', 'created_at', 'updated_at', 'display_to_buyer', 'display_to_seller']


class FAQSerializer(serializers.ModelSerializer): 
    category = FAQCategorySerializer(read_only=True)
    class Meta: 
        model = FAQ
        exclude = ['id', 'uuid', 'created_at', 'updated_at', 'shown', 'display_to_buyer', 'display_to_seller']


class VideoGuideSerializer(serializers.ModelSerializer): 
    class Meta: 
        model = VideoGuide
        exclude = ['created_at', 'updated_at']

class GuideSerializer(serializers.ModelSerializer): 
    class Meta: 
        model = Guide
        exclude = ['id', 'uuid', 'created_at', 'updated_at', 'shown', 'display_to_buyer', 'display_to_seller']

class BlogCategorySerializer(serializers.ModelSerializer): 
    class Meta: 
        model = BlogCategory
        exclude = ['uuid', 'created_at', 'updated_at']

class BlogCommentsSerializer(serializers.ModelSerializer):
    blog = serializers.PrimaryKeyRelatedField(queryset=BlogPost.objects.all())
    
    def create(self, validated_data):
        validated_data['comment'] = self.clean_comment(validated_data.get('comment'))
        return BlogComments.objects.create(**validated_data)
    
    def update(self, instance, validated_data):
        instance.name = validated_data.get('name', instance.full_name)
        instance.email = validated_data.get('email', instance.email)
        instance.comment = self.clean_comment(validated_data.get('comment', instance.comment))
        instance.blog = validated_data.get('blog', instance.blog)
        instance.save()
        return instance
    
    def clean_comment(self, comment):
        # List of allowed tags and attributes can be customized based on your needs
        allowed_tags = ['b', 'i', 'u', 'strong', 'em', 'p', 'ul', 'ol', 'li', 'br']
        allowed_attrs = {}
        cleaned_comment = bleach.clean(comment, tags=allowed_tags, attributes=allowed_attrs, strip=True)
        return cleaned_comment
    
    class Meta: 
        model = BlogComments
        exclude = ['id', 'uuid']
        write_only = ['email']

class BlogSerializer(serializers.ModelSerializer): 
    category = BlogCategorySerializer(read_only=True)
    comments = BlogCommentsSerializer(many=True, read_only=True)
    eng_text = serializers.SerializerMethodField()
    swe_text = serializers.SerializerMethodField()
    
    class Meta: 
        model = BlogPost
        exclude = ['uuid', 'created_at', 'updated_at']

    # To make sure the HTMl content does not contain any style attributes such as 
    # font-size, color, etc. It's handled in front end.
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        soup = BeautifulSoup(representation['eng_text'], 'html.parser')
        
        # Remove style attributes
        for tag in soup.recursiveChildGenerator():
            if hasattr(tag, 'attrs'):
                tag.attrs = {key: value for key, value in tag.attrs.items() if key != 'style'}

        representation['eng_text'] = str(soup)
        return representation
    
    def get_eng_text(self, obj):
        return self.replace_relative_urls(obj.eng_text)

    def get_swe_text(self, obj):
        return self.replace_relative_urls(obj.swe_text)

    def replace_relative_urls(self, text):
        if not text:
            return text

        from bs4 import BeautifulSoup

        soup = BeautifulSoup(text, "html.parser")
        for img in soup.find_all("img"):
            if img.get("src").startswith("/media/"):
                img["src"] = get_full_media_url(img["src"])
        
        return str(soup)
        
        
class ContactFormSerializer(serializers.Serializer): 
    email = serializers.EmailField()
    full_name = serializers.CharField()
    phone_number = serializers.CharField(required=False)
    message = serializers.CharField()
    

class TestimonialSerializer(serializers.ModelSerializer): 
    class Meta: 
        model = Testimonial
        exclude = ['id', 'uuid', 'created_at']
        
        
class TermsOfPurchaseSerializer(serializers.Serializer): 
    terms_of_purchase = serializers.FileField(read_only=True)


class HelpTextSerializer(serializers.ModelSerializer):
    class Meta: 
        model = HelpText
        exclude = ['id', 'uuid', 'created_at', 'updated_at']


class InformationTextSerializer(serializers.ModelSerializer):
    class Meta: 
        model = InformationText
        exclude = ['id', 'uuid', 'created_at', 'updated_at']