from enum import Enum


class CompanyTypes(Enum):
    BUYER = 'Buyer'
    SELLER = 'Seller'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

    @classmethod
    def names(cls):
        return [choice.name for choice in cls]
    

class OrderStatuses(Enum):
    PENDING = 'Pending'
    PROCESSING = 'Processing'
    SHIPPED = 'Shipped'
    DELIVERED = 'Delivered'
    CANCELLED = 'Cancelled'
    REFUNDED = 'Refunded'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
    @classmethod
    def values(cls):
        return [status.value for status in cls]

    @classmethod
    def keys(cls):
        return [status.name for status in cls]
    

class ReturnStatuses(Enum):
    REQUESTED = 'Requested'
    APPROVED = 'Approved'
    REJECTED = 'Rejected'
    RECEIVED = 'Received'
    REFUNDED = 'Refunded'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class ExchangeStatuses(Enum):
    REQUESTED = 'Requested'
    APPROVED = 'Approved'
    SHIPPED = 'Shipped'
    COMPLETED = 'Completed'
    REJECTED = 'Rejected'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class ReturnReasons(Enum):
    DOESNT_FIT = "Item doesn't fit"
    NOT_AS_DESCRIBED = "Not as described"
    DAMAGED = "Damaged or defective"
    WRONG_ITEM = "Received wrong item"
    CHANGED_MIND = "Changed mind"
    OTHER = "Other"

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class EventTypes(Enum):
    CREATE = 'Create'
    UPDATE = 'Update'
    DELETE = 'Delete'
    LOGIN = 'Login'
    LOGOUT = 'Logout'
    VIEW = 'View'
    OTHER = 'Other'
    
    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class NotificationTypes(Enum):
    ORDER_STATUS_UPDATE = 'Order Status Update'
    RETURN_STATUS_UPDATE = 'Return Status Update'
    EXCHANGE_STATUS_UPDATE = 'Exchange Status Update'
    PAYMENT_RECEIVED = 'Payment Received'
    SHIPMENT_UPDATE = 'Shipment Update'
    GENERAL = 'General Notification'
    NEW_ORDER = 'New Order'
    RETURN_REQUESTED = 'Return Requested'
    EXCHANGE_REQUESTED = 'Exchange Requested'
    ORDER_STATUS_CHANGED = 'Order Status Changed'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class PaymentMethods(Enum):
    CARD = 'Card'
    INVOICE = 'Invoice'
    

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class PaymentPlans(Enum):
    QUARTERLY = 'Quarterly'
    YEARLY = 'Yearly'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class SubscriptionAddOns(Enum):
    USER_ACCOUNT = 'User Account'
    ACCOUNTING_INTEGRATION = 'Accounting Integration'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class PaymentTransactionStatuses(Enum):
    PENDING = 'Pending'
    PAID = 'Paid'
    FAILED = 'Failed'
    CANCELLED = 'Cancelled'
    REFUNDED = 'Refunded'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class PaymentTransactionTypes(Enum):
    SUBSCRIPTION = 'Subscription'
    ADDON = 'Addon'
    UPGRADE = 'Upgrade'
    

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class DiscountTypes(Enum):
    PERCENTAGE = 'Percentage'
    AMOUNT = 'Amount'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class VisibilityTypes(Enum):
    PRODUCT = 'Product'
    VARIANT = 'Product Variant'
    CATEGORY = 'Product Category'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    

class WarehouseStrategyOptions(Enum):
    NEAREST = 'Nearest'
    MOST_STOCK = 'Most Stock'
    PRIORITY = 'Priority'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
    @classmethod
    def values(cls):
        return [status.value for status in cls]
