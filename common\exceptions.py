from rest_framework import status

from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException, ErrorDetail
from rest_framework.exceptions import ValidationError as DRFValidationError
from ast import literal_eval

def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)

    if response is not None:
        customized_response = {}
        status_code = response.status_code

        def extract_code(value):
            if isinstance(value, ErrorDetail) and value.code not in ['invalid', 'invalid_choice', 'required', 'does_not_exist', 'unique']:
                return int(value.code)
            elif isinstance(value, dict) and 'code' in value:
                if value['code'] not in ['invalid', 'invalid_choice', 'required', 'does_not_exist', 'unique']:
                    return int(value['code'])
            elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], ErrorDetail):
                if value[0].code not in ['invalid', 'invalid_choice', 'required', 'does_not_exist', 'unique']:
                    return int(value[0].code)
            return None

        def format_error_message(message):
            if isinstance(message, str):
                try:
                    # Try to parse the string as a dictionary
                    parsed_dict = literal_eval(message)
                    if isinstance(parsed_dict, dict) and 'detail' in parsed_dict:
                        return str(parsed_dict['detail'])
                except (ValueError, SyntaxError):
                    pass
            if isinstance(message, list):
                return ", ".join(str(item) for item in message)
            if isinstance(message, dict) and 'detail' in message:
                return str(message['detail'])
            return str(message)

        if isinstance(exc, DRFValidationError):
            if isinstance(exc.detail, dict):
                for key, value in exc.detail.items():
                    code = extract_code(value)
                    if code:
                        status_code = code
                    if isinstance(value, dict) and 'eng' in value and 'swe' in value:
                        customized_response[key] = {
                            'eng': format_error_message(value['eng']),
                            'swe': format_error_message(value['swe'])
                        }
                    elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], ErrorDetail):
                        customized_response[key] = format_error_message(str(value[0]))
                    elif isinstance(value, ErrorDetail): 
                        customized_response[key] = format_error_message(str(value))
                    else:
                        customized_response[key] = {
                            'eng': format_error_message(value),
                            'swe': format_error_message(value)  # You might want to translate this
                        }
            elif isinstance(exc.detail, list):
                customized_response = {
                    'eng': format_error_message(exc.detail),
                    'swe': format_error_message(exc.detail)  # You might want to translate this
                }
            else:
                customized_response = {
                    'eng': format_error_message(exc.detail),
                    'swe': format_error_message(exc.detail)  # You might want to translate this
                }
        else:
            eng_message = format_error_message(response.data.get('eng', response.data))
            swe_message = format_error_message(response.data.get('swe', response.data))
            
            customized_response = {
                'eng': eng_message,
                'swe': swe_message
            }

        customized_response['status_code'] = status_code
        response.data = customized_response
        response.status_code = status_code

    return response

class CustomAPIException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = {
        "eng": "A server error occurred.",
        "swe": "Ett serverfel inträffade."
    }
    default_code = 'error'

    def __init__(self, detail=None, code=None):
        if detail is None:
            detail = self.default_detail
        if code is None:
            code = self.default_code

        if isinstance(detail, str):
            detail = {
                "eng": detail,
                "swe": detail  # You might want to translate this
            }

        super().__init__(detail, code)