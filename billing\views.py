from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .serializers import (
    PlanSerializer, PaymentTransactionSerializer, AddonSerializer, SubscriptionSerializer,
    SignupSerializer, ChangePaymentMethodSerializer, ChangePaymentPlanSerializer, ChangePlanSerializer,
    AddAddonSerializer, RemoveAddonSerializer, CancelSubscriptionSerializer
)
from .models import Plan, PaymentTransaction, Addon, Subscription, SubscriptionAddon
from rest_framework.views import APIView
from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import AllowAny
from rest_framework.exceptions import ValidationError as DRFValidationError


def get_company_from_user(user):
    if hasattr(user, 'buyer_employee_profile'):
        return user.buyer_employee_profile.buyer_company.company
    elif hasattr(user, 'seller_employee_profile'):
        return user.seller_employee_profile.seller_company.company
    else:
        return None

class PlanViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Plan.objects.all()
    serializer_class = PlanSerializer
    lookup_field = 'uuid'
    internal_endpoint = True

class PaymentTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = PaymentTransactionSerializer
    lookup_field = 'uuid'
    internal_endpoint = True

    def get_queryset(self):
        company = get_company_from_user(self.request.user)
        if company is None:
            return PaymentTransaction.objects.none()
        return PaymentTransaction.objects.filter(subscription__company=company)

class AddonViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Addon.objects.all()
    serializer_class = AddonSerializer
    lookup_field = 'uuid'
    internal_endpoint = True

class SubscriptionViewSet(viewsets.ModelViewSet):
    serializer_class = SubscriptionSerializer
    lookup_field = 'uuid'
    internal_endpoint = True

    def get_queryset(self):
        company = get_company_from_user(self.request.user)
        if company is None:
            return Subscription.objects.none()
        return Subscription.objects.filter(company=company)

    @swagger_auto_schema(
        request_body=ChangePlanSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def request_upgrade(self, request, uuid=None):
        subscription = self.get_object()
        serializer = ChangePlanSerializer(data=request.data)
        if serializer.is_valid():
            new_plan_id = request.data.get('new_plan_id')
            new_plan = get_object_or_404(Plan, id=new_plan_id)
        
            old_plan, new_plan = subscription.change_plan(new_plan)
            return Response({
                'eng': f'Upgraded from {old_plan.name} to {new_plan.name}',
                'swe': f'Uppgradering från {old_plan.name} till {new_plan.name}',
                'new_cost': subscription.cost
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=ChangePlanSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def request_downgrade(self, request, uuid=None):
        subscription = self.get_object()
        serializer = ChangePlanSerializer(data=request.data)
        if serializer.is_valid():
            new_plan_id = request.data.get('new_plan_id')
            new_plan = get_object_or_404(Plan, id=new_plan_id)
        
            old_plan, new_plan = subscription.change_plan(new_plan)
            return Response({
                'eng': f'Downgrade from {old_plan.name} to {new_plan.name} scheduled for next billing cycle',
                'swe': f'Nedgradering från {old_plan.name} till {new_plan.name} schemalagd för nästa betalcykel',
                'new_cost': subscription.cost
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=CancelSubscriptionSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def request_cancellation(self, request, uuid=None):
        subscription = self.get_object()
        serializer = CancelSubscriptionSerializer(data=request.data)
        if serializer.is_valid():
            subscription.cancel(reason=serializer.validated_data['reason'])
            return Response({
                'eng': 'Subscription cancelled. Service will be available until the end of the current billing period.',
                'swe': 'Prenumerationen har avslutats. Tjänsten kommer att vara tillgänglig tills slutet på den aktuella betalningsperioden.'
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=AddAddonSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def add_addon(self, request, uuid=None):
        subscription = self.get_object()
        serializer = AddAddonSerializer(data=request.data)
        if serializer.is_valid():
            addon_id = request.data.get('addon_id')
            quantity = request.data.get('quantity', 1)
        
            addon = get_object_or_404(Addon, id=addon_id)
            SubscriptionAddon.objects.create(subscription=subscription, addon=addon, quantity=quantity)
            subscription.save()  # This will recalculate the cost
        
            return Response({
                'eng': f'Added {quantity} {addon.name} to the subscription',
                'swe': f'Lade till {quantity} {addon.name} i prenumerationen',
                'new_cost': subscription.cost
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=RemoveAddonSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def remove_addon(self, request, uuid=None):
        subscription = self.get_object()
        serializer = RemoveAddonSerializer(data=request.data)
        if serializer.is_valid():
            subscription_addon_id = request.data.get('subscription_addon_id')
        
            subscription_addon = get_object_or_404(SubscriptionAddon, subscription=subscription, id=subscription_addon_id)
            subscription_addon.delete()
            subscription.save()  # This will recalculate the cost
            return Response({
                'eng': f'Addon removed from the subscription',
                'swe': f'Lade bort tillägg från prenumerationen',
                'new_cost': subscription.cost
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=ChangePaymentPlanSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def change_payment_plan(self, request, uuid=None):
        subscription = self.get_object()
        serializer = ChangePaymentPlanSerializer(data=request.data)
        if serializer.is_valid():
            new_payment_plan = request.data.get('new_payment_plan')
        
            if new_payment_plan not in dict(Subscription.payment_plan.field.choices).keys():
                return Response({'eng': 'Invalid payment plan', 'swe': 'Ogiltig betalningsplan'}, status=status.HTTP_400_BAD_REQUEST)
            
            subscription.payment_plan = new_payment_plan
            subscription.save()  # This will recalculate the cost
            return Response({
                'eng': f'Payment plan changed to {new_payment_plan}',
                'swe': f'Betalningsplanen har ändrats till {new_payment_plan}',
                'new_cost': subscription.cost
            })
        else:
            raise DRFValidationError(serializer.errors)

    @swagger_auto_schema(
        request_body=ChangePaymentMethodSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def change_payment_method(self, request, uuid=None):
        subscription = self.get_object()
        serializer = ChangePaymentMethodSerializer(data=request.data)
        if serializer.is_valid():
            new_payment_method = request.data.get('new_payment_method')
        
            if new_payment_method not in dict(Subscription.payment_method.field.choices).keys():
                return Response({'eng': 'Invalid payment method', 'swe': 'Ogiltig betalningsmetod'}, status=status.HTTP_400_BAD_REQUEST)
            
            old_method, new_method = subscription.change_payment_method(new_payment_method)
            return Response({
                'eng': f'Payment method changed from {old_method} to {new_method}',
                'swe': f'Betalningsmetoden har ändrats från {old_method} till {new_method}'
            })
        else:
            raise DRFValidationError(serializer.errors)
    
    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass  

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

class SignupView(APIView):
    internal_endpoint = True
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        request_body=SignupSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @transaction.atomic
    def post(self, request):
        serializer = SignupSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                return Response({
                    'eng': 'User, company, and employee created successfully',
                    'swe': 'Användare, företag och anställd skapade',
                    'user_id': user.id,
                    'email': user.email,
                    'company_type': serializer.validated_data['company_type']
                }, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response({
                    'eng': 'An error occurred during signup',
                    'swe': 'Ett fel uppstod vid registrering',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else: 
            raise DRFValidationError(serializer.errors)


