from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SellerCompanyViewSet, SellerCompanySettingViewSet,
    SellerCompanyEmployeeViewSet, ProductCategoryViewSet, ProductViewSet,
    ProductImageViewSet, ProductVariantViewSet, DiscountViewSet,
    DiscountAssignmentViewSet, DiscountCodeViewSet, PriceListViewSet,
    ProductPriceViewSet, WarehouseViewSet, WarehouseInventoryViewSet,
    OrderCategoryViewSet, ReturnViewSet, ReturnItemViewSet,
    ExchangeViewSet, PackingSlipViewSet, DocumentCategoryViewSet,
    DocumentViewSet, SellerOrderViewSet, SellerNotificationViewSet, 
    SellerEventLogViewSet, SellerRoleViewSet, SellerPermissionViewSet,
    BuyerSellerRelationshipViewSet, SellerOrderNoteViewSet, VATRateViewSet,
    SellerBlogCategoryViewSet, SellerBlogPostViewSet, SellerFAQCategoryViewSet, 
    SellerFAQItemViewSet, VATCategoryViewSet, ProductVisibilityRuleViewSet
)

router = DefaultRouter()
router.register(r'companies', SellerCompanyViewSet)
# router.register(r'settings', SellerCompanySettingViewSet)
router.register(r'employees', SellerCompanyEmployeeViewSet)
router.register(r'product-categories', ProductCategoryViewSet)
router.register(r'products', ProductViewSet)
router.register(r'product-images', ProductImageViewSet)
router.register(r'product-variants', ProductVariantViewSet)
router.register(r'discounts', DiscountViewSet)
router.register(r'discount-assignments', DiscountAssignmentViewSet)
router.register(r'discount-codes', DiscountCodeViewSet)
router.register(r'price-lists', PriceListViewSet)
router.register(r'product-prices', ProductPriceViewSet)
router.register(r'warehouses', WarehouseViewSet)
router.register(r'warehouse-inventory', WarehouseInventoryViewSet)
router.register(r'order-categories', OrderCategoryViewSet)
router.register(r'returns', ReturnViewSet)
router.register(r'exchanges', ExchangeViewSet)
# router.register(r'packing-slips', PackingSlipViewSet)
router.register(r'document-categories', DocumentCategoryViewSet)
router.register(r'documents', DocumentViewSet)
router.register(r'orders', SellerOrderViewSet, basename='sellerorder')
router.register(r'permissions', SellerPermissionViewSet, basename='sellerpermission')
router.register(r'roles', SellerRoleViewSet, basename='sellerrole')
router.register(r'event-logs', SellerEventLogViewSet, basename='sellereventlog')
router.register(r'notifications', SellerNotificationViewSet, basename='sellernotification')
router.register(r'buyer-relationships', BuyerSellerRelationshipViewSet, basename='buyersellerrelationship')
router.register(r'order-notes', SellerOrderNoteViewSet)
router.register(r'vat-rates', VATRateViewSet)
router.register(r'blog-categories', SellerBlogCategoryViewSet)
router.register(r'blog-posts', SellerBlogPostViewSet)
router.register(r'faq-categories', SellerFAQCategoryViewSet)
router.register(r'faq-items', SellerFAQItemViewSet)
router.register(r'vat-categories', VATCategoryViewSet)
router.register(r'product-visibility-rules', ProductVisibilityRuleViewSet)


urlpatterns = [
    path('', include(router.urls)),
]