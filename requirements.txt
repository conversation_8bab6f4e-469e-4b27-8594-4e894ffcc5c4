amqp==5.2.0
asgiref==3.8.1
attrs==23.2.0
beautifulsoup4==4.12.3
billiard==4.2.0
bleach==6.1.0
boto3==1.34.143
botocore==1.34.143
bs4==0.0.2
cattrs==23.2.3
celery==5.4.0
certifi==2024.7.4
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
coreapi==2.3.3
coreschema==0.0.4
cron-descriptor==1.4.3
cryptography==42.0.8
Django==4.2.14
django-appconf==1.0.6
django-celery-beat==2.6.0
django-celery-results==2.5.1
django-ckeditor==6.7.1
django-cors-headers==4.4.0
django-cryptography==1.1
django-filter==23.5
django-inmemorystorage==0.1.1
django-js-asset==2.2.0
django-model-utils==4.5.1
django-timezone-field==7.0
django_aio @ git+ssh://*****************/ophefard/django-aio.git@f1792b2c560279b972a297eedef065dcc93396c6
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
drf-yasg==1.21.7
factory-boy==3.3.0
Faker==19.13.0
filelock==3.12.4
gevent==23.9.1
greenlet==3.0.3
idna==3.7
inflection==0.5.1
itypes==1.2.0
Jinja2==3.1.4
jmespath==1.0.1
jsonschema==4.17.3
kombu==5.3.7
MarkupSafe==2.1.5
mmh3==4.1.0
numpy==1.26.4
packaging==24.1
pandas==2.2.2
pillow==10.4.0
platformdirs==4.2.2
pottery==3.0.0
prompt_toolkit==3.0.47
psycopg2-binary==2.9.9
pycparser==2.22
PyJWT==2.8.0
pyrsistent==0.20.0
python-crontab==3.2.0
python-dateutil==2.9.0.post0
pytz==2024.1
PyYAML==6.0.1
ratelimit==2.2.1
redis==4.6.0
requests==2.32.3
requests-cache==1.1.1
s3transfer==0.10.2
six==1.16.0
soupsieve==2.6
sqlparse==0.5.0
stripe==6.7.0
traceback-with-variables==2.0.4
typing_extensions==4.12.2
tzdata==2024.1
uritemplate==4.1.1
url-normalize==1.4.3
urllib3==2.2.2
vine==5.1.0
wcwidth==0.2.13
webencodings==0.5.1
zope.event==5.0
zope.interface==6.4.post2
