from django.db import models
from common.models import Company, BaseModel, Account, Role
from django.core.exceptions import ValidationError

optional = {
    "null": True,
    "blank": True
}

class BuyerSellerEmployeeAccess(BaseModel):
    """
    Manages access rights for buyer company employees to seller companies.
    """
    buyer_employee = models.ForeignKey('buyer.BuyerCompanyEmployee', on_delete=models.CASCADE, related_name='seller_accesses')
    seller_company = models.ForeignKey('seller.SellerCompany', on_delete=models.CASCADE, related_name='buyer_employee_accesses')
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Credit limit for this employee with this seller")

    def __str__(self):
        return f"{self.buyer_employee} access to {self.seller_company}"
    
    class Meta:
        unique_together = ('buyer_employee', 'seller_company')
        ordering = ['-id']

    def clean(self):
        if not self.buyer_employee.buyer_company in self.seller_company.buyer_relationships.all():
            raise ValidationError("The buyer company must have a relationship with the seller company.")

class BuyerCompany(BaseModel):
    """
    Represents a company that acts as a buyer in the system.
    """
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='buyer_profile')
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Overall credit limit for the buyer company")

    def __str__(self):
        return f"Buyer: {self.company.name}"
    
    class Meta: 
        ordering = ['-id']

    def get_employees(self):
        return BuyerCompanyEmployee.objects.filter(buyer_company=self)

class BuyerCompanyEmployee(Account):
    """
    Represents an employee of a buyer company.
    """
    buyer_company = models.ForeignKey(BuyerCompany, on_delete=models.CASCADE, related_name='employees')
    user = models.OneToOneField('common.User', on_delete=models.CASCADE, related_name='buyer_employee_profile')
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, related_name='buyer_employees')
    is_active = models.BooleanField(default=True, help_text="Indicates if the employee is active in this role")

    class Meta:
        unique_together = ('user', 'buyer_company')
        ordering = ['-id']

    def __str__(self):
        return f"{self.user} - {self.buyer_company} ({self.role})"

    def has_access_to_seller(self, seller_company):
        return BuyerSellerEmployeeAccess.objects.filter(
            buyer_employee=self,
            seller_company=seller_company,
            buyer_employee__is_active=True
        ).exists()
    
    def has_perm(self, perm):
        from common.utils import has_permission
        return has_permission(self, perm)


class BuyerCompanySetting(BaseModel):
    """
    Stores additional settings for a buyer company.
    """
    buyer_company = models.OneToOneField(BuyerCompany, on_delete=models.CASCADE, related_name='settings')
    
    # Configure notification preferences
    notification_preferences = models.JSONField(default=dict, blank=True, help_text="Preferences for different types of notifications")
    
    # Manage shipping and billing addresses
    default_shipping_address = models.ForeignKey('Address', on_delete=models.SET_NULL, null=True, blank=True, related_name='default_shipping_for')
    default_billing_address = models.ForeignKey('Address', on_delete=models.SET_NULL, null=True, blank=True, related_name='default_billing_for')
    
    # Set contact information
    contact_name = models.CharField(max_length=255, blank=True)
    contact_email = models.EmailField(blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)

    def __str__(self):
        return f"Settings for {self.buyer_company}"
    
    class Meta: 
        ordering = ['-id']

# You might need to create an Address model if you haven't already
class Address(BaseModel):
    buyer_company = models.ForeignKey(BuyerCompany, on_delete=models.CASCADE, related_name='addresses')
    address_type = models.CharField(max_length=20, choices=[('SHIPPING', 'Shipping'), ('BILLING', 'Billing')])
    street_address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=20)
    country_code = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.address_type} address for {self.buyer_company.company.name}"
    
    class Meta: 
        ordering = ['-id']


class BuyerOrderNote(BaseModel):
    """
    Represents a note for an order.
    """
    buyer_company = models.ForeignKey(BuyerCompany, on_delete=models.CASCADE, related_name='order_notes')
    order = models.ForeignKey('common.Order', on_delete=models.CASCADE, related_name='buyer_order_notes')
    note = models.TextField()