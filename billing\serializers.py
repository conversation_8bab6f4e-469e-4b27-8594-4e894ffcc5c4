from rest_framework import serializers
from .models import Plan, PaymentTransaction, Addon, Subscription, SubscriptionAddon, Price
from common.enums import PaymentMethods, PaymentPlans, CompanyTypes
from common.models import Company, Role
from buyer.models import BuyerCompany, BuyerCompanyEmployee, Address
from seller.models import SellerCompany, SellerCompanyEmployee
from django.contrib.auth import get_user_model
from datetime import date
from rest_framework.exceptions import ValidationError as DRFValidationError
from django.db import transaction
from django.db.models import Q
from django.contrib.contenttypes.models import ContentType


User = get_user_model()

class PriceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Price
        exclude = [
            'created_at', 'updated_at', 'stripe_product_id', 'accounting_product_id', 'quarterly_stripe_payment_link', 
            'yearly_stripe_payment_link', 'stripe_price_id_quarterly', 'stripe_price_id_yearly'
        ]

class PlanSerializer(serializers.ModelSerializer):
    price = PriceSerializer()

    class Meta:
        model = Plan
        exclude = ['created_at', 'updated_at']

class PaymentTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentTransaction
        exclude = ['created_at', 'updated_at', 'stripe_id']

class AddonSerializer(serializers.ModelSerializer):
    price = PriceSerializer()

    class Meta:
        model = Addon
        exclude = ['created_at', 'updated_at']

class SubscriptionAddonSerializer(serializers.ModelSerializer):
    addon = AddonSerializer()

    class Meta:
        model = SubscriptionAddon
        fields = ['addon', 'quantity']

class SubscriptionSerializer(serializers.ModelSerializer):
    plan = PlanSerializer()
    addons = SubscriptionAddonSerializer(source='subscriptionaddon_set', many=True, read_only=True)

    class Meta:
        model = Subscription
        fields = ['id', 'uuid', 'company', 'plan', 'payment_plan', 'payment_method', 'start_date', 'end_date', 'is_active', 'addons', 'cost']


class ChangePlanSerializer(serializers.Serializer):
    new_plan_id = serializers.IntegerField()

class AddAddonSerializer(serializers.Serializer):
    addon_id = serializers.IntegerField()
    quantity = serializers.IntegerField()

class RemoveAddonSerializer(serializers.Serializer):
    subscription_addon_id = serializers.IntegerField()

class ChangePaymentMethodSerializer(serializers.Serializer):
    new_payment_method = serializers.ChoiceField(choices=PaymentMethods.choices())

class ChangePaymentPlanSerializer(serializers.Serializer):
    new_payment_plan = serializers.ChoiceField(choices=PaymentPlans.choices())

class CancelSubscriptionSerializer(serializers.Serializer):
    reason = serializers.CharField()


class SignupSerializer(serializers.Serializer):
    email = serializers.EmailField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    company_name = serializers.CharField()
    company_type = serializers.ChoiceField(choices=CompanyTypes.choices())
    organisation_number = serializers.CharField()
    vat_number = serializers.CharField(required=False, allow_blank=True)
    street_address = serializers.CharField()
    zip_code = serializers.CharField()
    city = serializers.CharField()
    country_code = serializers.CharField(max_length=2)
    contact_phone_number = serializers.CharField()

    # Fields for seller subscription
    plan_id = serializers.IntegerField(required=False)
    payment_plan = serializers.ChoiceField(choices=PaymentPlans.choices(), required=False)
    payment_method = serializers.ChoiceField(choices=PaymentMethods.choices(), required=False)

    def validate(self, data):
        if data['company_type'] == CompanyTypes.SELLER.name:
            if not all([data.get('plan_id'), data.get('payment_plan'), data.get('payment_method')]):
                raise DRFValidationError({"eng": "Sellers must select package, payment plan, and payment method.", "swe": "Säljare måste välja paket, betalningsplan och betalningsmetod."})
        
        # Check for existing companies
        existing_company = Company.objects.filter(
            Q(name=data['company_name']) | Q(organisation_number=data['organisation_number'])
        ).first()

        if existing_company:
            if data['company_type'] == CompanyTypes.BUYER.name:
                if BuyerCompany.objects.filter(company=existing_company).exists():
                    raise DRFValidationError({"eng": "A buyer company with this name or organisation number already exists.", "swe": "Ett köparföretag med detta namn eller organisationsnummer finns redan."})
            else:
                if SellerCompany.objects.filter(company=existing_company).exists():
                    raise DRFValidationError({"eng": "A seller company with this name or organisation number already exists.", "swe": "Ett säljarföretag med detta namn eller organisationsnummer finns redan."})

        return data

    def create(self, validated_data):
        with transaction.atomic():
            user = User.objects.filter(email=validated_data['email']).first()

            if not user:
                user = User.objects.create_user(
                    email=validated_data['email'],
                    first_name=validated_data['first_name'],
                    last_name=validated_data['last_name'],
                )

            # Check if company already exists
            company = Company.objects.filter(
                Q(name=validated_data['company_name']) | Q(organisation_number=validated_data['organisation_number'])
            ).first()

            if not company:
                company = Company.objects.create(
                    name=validated_data['company_name'],
                    organisation_number=validated_data['organisation_number'],
                    vat_number=validated_data.get('vat_number', ''),
                    street_address=validated_data['street_address'],
                    zip_code=validated_data['zip_code'],
                    city=validated_data['city'],
                    country_code=validated_data['country_code'],
                    contact_phone_number=validated_data['contact_phone_number'],
                    contact_email=validated_data['email'],
                )

            if validated_data['company_type'] == CompanyTypes.BUYER.name:
                buyer_company = BuyerCompany.objects.create(company=company)
                BuyerCompanyEmployee.objects.create(
                    buyer_company=buyer_company,
                    user=user, 
                    account_name=f"{user.first_name} {user.last_name}",
                    role=Role.objects.filter(name="Administratör", content_type=ContentType.objects.get_for_model(BuyerCompany), object_id=buyer_company.id).first()
                )
                Address.objects.create(
                    buyer_company=buyer_company,
                    address_type="BILLING",
                    street_address=validated_data['street_address'],
                    city=validated_data['city'],
                    zip_code=validated_data['zip_code'],
                    country_code=validated_data['country_code'],
                )
            else:
                seller_company = SellerCompany.objects.create(company=company)
                SellerCompanyEmployee.objects.create(
                    seller_company=seller_company,
                    user=user,
                    role=Role.objects.filter(name="Administratör", content_type=ContentType.objects.get_for_model(SellerCompany), object_id=seller_company.id).first()
                )
                
                # Create subscription for seller
                plan = Plan.objects.get(id=validated_data['plan_id'])
                Subscription.objects.create(
                    company=company,
                    plan=plan,
                    payment_plan=validated_data['payment_plan'],
                    payment_method=validated_data['payment_method'],
                    start_date=date.today(),
                )

        return user



