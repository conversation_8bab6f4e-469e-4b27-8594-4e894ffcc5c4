from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework.documentation import include_docs_urls
from .schema_generators import schema_view_internal, schema_view_external


urlpatterns = [
    path("admin/", admin.site.urls),
    # other URL patterns for your project
    path('api/v1/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/v1/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    # include app-specific URLs here
    path("api/v1/common/", include("common.urls")),
    path("api/v1/internal/", include("internal.urls")),
    path("api/v1/billing/", include("billing.urls")),
    path("api/v1/seller/", include("seller.urls")),
    path("api/v1/buyer/", include("buyer.urls")),
    # path("api/v1/individual-customer/", include("individual_customer.urls")),
    path('api/docs-ui/', include_docs_urls(title='API Docs', schema_url='api_docs_schema')),
    path('api/docs/internal/swagger/', schema_view_internal.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui-internal'),
    path('api/docs/internal/redoc/', schema_view_internal.with_ui('redoc', cache_timeout=0), name='schema-redoc-ui-internal'),
    path('api/docs/external/swagger/', schema_view_external.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui-external'),
    path('api/docs/external/redoc/', schema_view_external.with_ui('redoc', cache_timeout=0), name='schema-redoc-ui-external'),
    path('ckeditor/', include('ckeditor_uploader.urls')),
    path('tinymce/', include('tinymce.urls')),
]


from django.conf import settings
from django.conf.urls.static import static
# This configuration is only used during development. Django does 
# not serve media files in production.

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

