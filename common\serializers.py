from django.contrib.auth import get_user_model
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.exceptions import ValidationError as DRFValidationError
from django.utils.translation import gettext_lazy as _
from .models.regular import (
    Company, User, Permission, Role, EventLog, Order, OrderItem, OrderStatusHistory, 
    Notification, Country, ProductReview
)
from utils.base_serializers import BaseModelSerializer

User = get_user_model()

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # Add custom claims
        token['email'] = user.email
        return token

class SetPasswordSerializer(serializers.Serializer):
    new_password1 = serializers.CharField(required=True, write_only=True)
    new_password2 = serializers.CharField(required=True, write_only=True)
    uidb64 = serializers.CharField(write_only=True, required=True)
    token = serializers.CharField(write_only=True, required=True)

    def validate(self, attrs):
        if attrs['new_password1'] != attrs['new_password2']:
            raise DRFValidationError({
                "eng": "The two password fields didn't match.",
                "swe": "De två lösenordsfälten matchade inte."
            })
        try:
            validate_password(attrs['new_password1'])
        except DRFValidationError as e:
            raise 
        return attrs

    def save(self, **kwargs):
        user = self.context['user']
        password = self.validated_data['new_password1']
        user.set_password(password)
        if not user.is_verified:
            user.is_verified = True
        user.save()
        return user

class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(write_only=True, required=True)

    class Meta:
        fields = ['email']




class CompanySerializer(BaseModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'

class UserSerializer(BaseModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'uuid', 'email', 'first_name', 'last_name', 'full_name', 'phone', 'is_active', 'is_verified', 'last_login']
        read_only_fields = ['id', 'uuid', 'is_active', 'is_verified', 'last_login']


class NestedUserSerializer(BaseModelSerializer):
    email = serializers.EmailField(required=False)
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'full_name', 'phone', 'email']
        read_only_fields = ['id']

# Used for inviting employees, the email validation happens in its respective serializer
class UserSerializerWithoutEmail(serializers.ModelSerializer):
    
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'full_name', 'phone']

class OrderStatusHistorySerializer(BaseModelSerializer):
    class Meta:
        model = OrderStatusHistory
        fields = '__all__'

class BaseOrderItemSerializer(BaseModelSerializer):
    class Meta:
        model = OrderItem
        fields = ['id', 'uuid', 'quantity', 'price', 'total_amount', 'description', 'expected_delivery_date', 'shipped_at', 'shipped_quantity']
        read_only_fields = ['id', 'uuid', 'total_amount']


class BaseOrderListSerializer(BaseModelSerializer):
    class Meta:
        model = Order
        fields = ['id', 'uuid', 'order_number', 'status', 'total_amount_to_pay', 'total_discount_amount', 'total_vat_amount', 'placed_at']

class BaseOrderDetailSerializer(BaseModelSerializer):
    items = BaseOrderItemSerializer(many=True)
    status_history = OrderStatusHistorySerializer(many=True, read_only=True)
    discount_code = serializers.CharField(required=False, allow_null=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'uuid', 'order_number', 'buyer_company', 'seller_company', 'status', 'total_amount_to_pay', 
            'total_discount_amount', 'total_vat_amount', 'items', 'placed_at', 'discount_code'
        ]
        read_only_fields = ['id', 'uuid', 'order_number', 'status', 'total_amount_to_pay', 'total_discount_amount', 'total_vat_amount', 'placed_at']

    def create(self, validated_data):
        items_data = validated_data.pop('items')
        order = Order.objects.create(**validated_data)
        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)
        return order

class BasePermissionSerializer(BaseModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'description']

class BaseRoleSerializer(BaseModelSerializer):
    permissions = serializers.PrimaryKeyRelatedField(many=True, queryset=Permission.objects.all())

    custom_fields = {
        "permissions": BasePermissionSerializer
    }
    class Meta:
        model = Role
        fields = ['id', 'uuid', 'created_at', 'updated_at', 'name', 'permissions', 'content_type', 'object_id']

class BaseEventLogSerializer(BaseModelSerializer):
    class Meta:
        model = EventLog
        fields = ['id', 'user', 'event_type', 'content_type', 'object_id', 'description', 'ip_address', 'additional_data', 'created_at']

class BaseNotificationSerializer(BaseModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'uuid', 'content_type', 'object_id', 'notification_type', 'title', 'message', 'is_read', 'created_at', 'read_at']




class BaseCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = [
            'name', 'organisation_number', 'vat_number', 'street_address', 'zip_code', 
            'city', 'contact_phone_number', 'contact_email', 'country_code', 'logo'
        ]



class CountrySerializer(BaseModelSerializer):
    class Meta:
        model = Country
        fields = ['id', 'code', 'name']


class ProductReviewSummarySerializer(serializers.ModelSerializer):
    buyer_name = serializers.SerializerMethodField()

    class Meta:
        model = ProductReview
        fields = ['id', 'buyer_name', 'rating', 'comment', 'created_at']

    def get_buyer_name(self, obj):
        return obj.buyer.user.get_full_name()

class ProductReviewSerializer(serializers.ModelSerializer):
    from seller.models import Product, ProductVariant
    buyer_name = serializers.SerializerMethodField()
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), required=False, allow_null=True)
    product_variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False, allow_null=True)

    custom_fields = {
        'buyer': 'buyer.BuyerCompanyEmployeeSerializer',
        'product': 'seller.serializers.ProductListSerializer',
        'product_variant': 'seller.serializers.ProductVariantSerializer'
    }

    class Meta:
        model = ProductReview
        fields = ['id', 'product', 'product_variant', 'buyer', 'buyer_name', 'rating', 'comment', 'is_verified_purchase', 'created_at']
        read_only_fields = ['buyer', 'is_verified_purchase']

    def get_buyer_name(self, obj):
        return obj.buyer.user.get_full_name()

    def to_internal_value(self, data):
        # First, let the parent class do its normal conversion
        internal_value = super().to_internal_value(data)

        # Now, we can work with the converted data
        if 'product' in internal_value:
            seller_company = internal_value['product'].seller_company
        elif 'product_variant' in internal_value:
            seller_company = internal_value['product_variant'].product.seller_company
        else:
            # If neither product nor product_variant is provided, we can't determine the seller_company
            raise serializers.ValidationError({"eng": "Either product or product variant must be provided", 
                                               "swe": "Antingen produkt eller produktvariant måste anges"})

        internal_value['seller_company'] = seller_company
        return internal_value

    def validate(self, data):
        if ('product' in data and 'product_variant' in data) and (data['product'] and data['product_variant']):
            raise DRFValidationError({"eng":"Specify either a product or a product variant, not both.", "swe":"Ange antingen en produkt eller en produktvariant, inte båda."})
        if ('product' not in data and 'product_variant' not in data) or (not data.get('product') and not data.get('product_variant')):
            raise DRFValidationError({"eng":"You must specify either a product or a product variant.", "swe":"Du måste ange antingen en produkt eller en produktvariant."})
        return data

    def create(self, validated_data):
        user = self.context['request'].user
        buyer = user.buyer_employee_profile
        validated_data['buyer'] = buyer

        # Check if this is a verified purchase
        product = validated_data.get('product') or validated_data.get('product_variant').product
        order_items = OrderItem.objects.filter(
            order__buyer_company=buyer.buyer_company,
            product=product
        )
        validated_data['is_verified_purchase'] = order_items.exists()

        return super().create(validated_data)




class PublicProductVariantSerializer(BaseModelSerializer):
    price = serializers.SerializerMethodField()
    
    class Meta:
        from seller.models import ProductVariant
        model = ProductVariant
        fields = ['id', 'uuid', 'name', 'sku', 'price']
    
    def get_price(self, obj):
        request = self.context.get('request')
        buyer_company = None
        if request and request.user.is_authenticated:
            if hasattr(request.user, 'buyer_employee_profile'):
                buyer_company = request.user.buyer_employee_profile.buyer_company
        
        price = obj.get_price(buyer_company)
        discounted_price, discount_percentage = obj.get_price_with_discount(buyer_company)
        
        return {
            'base_price': price,
            'discounted_price': discounted_price,
            'discount_percentage': discount_percentage
        }

class PublicProductSerializer(BaseModelSerializer):
    from seller.serializers import ProductImageSerializer
    images = ProductImageSerializer(many=True, read_only=True)
    variants = PublicProductVariantSerializer(many=True, read_only=True)
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2, read_only=True)
    price = serializers.SerializerMethodField()

    class Meta:
        from seller.models import Product
        model = Product
        fields = ['id', 'uuid', 'name', 'description', 'images', 'variants', 'average_rating', 'price']


    def get_price(self, obj):
        request = self.context.get('request')
        buyer_company = None
        if request and request.user.is_authenticated:
            if hasattr(request.user, 'buyer_employee_profile'):
                buyer_company = request.user.buyer_employee_profile.buyer_company
        
        price = obj.get_price(buyer_company)
        discounted_price, discount_percentage = obj.get_price_with_discount(buyer_company)
        
        return {
            'base_price': price,
            'discounted_price': discounted_price,
            'discount_percentage': discount_percentage
        }