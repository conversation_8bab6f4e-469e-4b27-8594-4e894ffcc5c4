from test_base.base import BaseTestCase
from decimal import Decimal
from test_base.factories import (
    ProductCategoryFactory, ProductFactory, ProductVariantFactory,
    DiscountFactory, DiscountAssignmentFactory, DiscountCodeFactory, PriceListFactory,
    ProductPriceFactory, WarehouseFactory, WarehouseInventoryFactory, OrderCategoryFactory,
    ReturnFactory, ReturnItemFactory, ExchangeFactory, PackingSlipFactory,
    DocumentCategoryFactory, DocumentFactory, BuyerCompanyFactory, OrderFactory,
    OrderItemFactory
)
from seller.models import BuyerSellerRelationship
from common.enums import ReturnStatuses, ExchangeStatuses, DiscountTypes
from django.contrib.contenttypes.models import ContentType
from common.utils import make_aware_datetime
from datetime import datetime, timedelta
from seller.utils import calculate_discounted_price
import os
from django.conf import settings

class SellerCompanyModelTest(BaseTestCase):
    def test_seller_company_creation(self):
        self.assertIsNotNone(self.seller_company.company)
        self.assertEqual(self.seller_company.company.name, "Test Company")
        self.assertTrue(self.seller_company.webshop_is_public)

    def test_seller_company_str_method(self):
        expected_str = "Seller: Test Company"
        self.assertEqual(str(self.seller_company), expected_str)

class SellerCompanyEmployeeModelTest(BaseTestCase):
    def test_seller_employee_creation(self):
        self.assertIsNotNone(self.seller_employee.user)
        self.assertIsNotNone(self.seller_employee.seller_company)
        self.assertIsNotNone(self.seller_employee.role)
        self.assertEqual(self.seller_employee.user.email, "<EMAIL>")
        self.assertEqual(self.seller_employee.seller_company, self.seller_company)

    def test_seller_employee_str_method(self):
        expected_str = f"{self.seller_employee.user} - {self.seller_employee.seller_company} ({self.seller_employee.role})"
        self.assertEqual(str(self.seller_employee), expected_str)

class ProductModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product = ProductFactory(
            seller_company=self.seller_company,
            name="Test Product",
            description="A test product",
            base_price=Decimal("100.00")
        )
        self.price_list = PriceListFactory(seller_company=self.product.seller_company, is_default=True)
        self.product_price = ProductPriceFactory(price_list=self.price_list, product=self.product, price=Decimal('110.00'))

    def test_product_creation(self):
        self.assertEqual(self.product.name, "Test Product")
        self.assertEqual(self.product.base_price, Decimal("100.00"))

    def test_product_str_method(self):
        expected_str = f"Test Product - {self.seller_company}"
        self.assertEqual(str(self.product), expected_str)

    def test_get_price_method_without_relationship(self):
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=None)
        price = self.product.get_price(self.buyer_company)
        self.assertEqual(price, Decimal('110.00'))

    def test_get_price_method_with_relationship(self):
        price_list = PriceListFactory(seller_company=self.seller_company)
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=price_list)
        ProductPriceFactory(price_list=price_list, product=self.product, price=Decimal('120.00'))
        price = self.product.get_price(self.buyer_company)
        self.assertEqual(price, Decimal('120.00'))

    def test_get_price_with_discount(self):
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=None)
        discount = DiscountFactory(
            seller_company=self.product.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('10.00'), 
            end_date=make_aware_datetime(datetime.now() + timedelta(days=1))
        )
        DiscountAssignmentFactory(discount=discount, content_object=self.product)
        
        discounted_price, discount_percentage = self.product.get_price_with_discount(self.buyer_company)
        expected_price = Decimal('99.00')  # 110 - 10%
        self.assertEqual(discounted_price, expected_price)
        self.assertEqual(discount_percentage, Decimal('10.00'))

class ProductVariantModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product = ProductFactory(
            seller_company=self.seller_company,
            name="Test Product",
            description="A test product",
            base_price=Decimal("100.00")
        )
        self.product_variant = ProductVariantFactory(
            product=self.product,
            name="Test Variant"
        )
        self.price_list = PriceListFactory(seller_company=self.seller_company, is_default=True)
        self.product_price = ProductPriceFactory(
            price_list=self.price_list,
            product=self.product_variant.product,
            product_variant=self.product_variant,
            price=Decimal('120.00'),
        )

    def test_product_variant_creation(self):
        self.assertIsNotNone(self.product_variant.product)
        self.assertIsNotNone(self.product_variant.name)
        self.assertEqual(self.product_variant.name, "Test Variant")
        self.assertEqual(self.product_variant.product, self.product)

    def test_product_variant_str_method(self):
        expected_str = "Test Product - Test Variant"
        self.assertEqual(str(self.product_variant), expected_str)

    def test_get_price_method_without_relationship(self):
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=None)
        price = self.product_variant.get_price(self.buyer_company)
        self.assertEqual(price, Decimal('120.00'))

    def test_get_price_method_with_relationship(self):
        price_list = PriceListFactory(seller_company=self.seller_company)
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=price_list)
        ProductPriceFactory(price_list=price_list, product=self.product, price=Decimal('130.00'))
        price = self.product_variant.get_price(self.buyer_company)
        self.assertEqual(price, Decimal('130.00'))

    def test_get_price_with_product_discount(self):
        BuyerSellerRelationship.objects.filter(buyer_company=self.buyer_company, seller_company=self.seller_company).update(price_list=None)
        discount = DiscountFactory(
            seller_company=self.product_variant.product.seller_company, discount_type=DiscountTypes.AMOUNT.name, value=Decimal('20.00'),
            end_date=make_aware_datetime(datetime.now() + timedelta(days=1))
        )
        DiscountAssignmentFactory(discount=discount, content_object=self.product_variant.product)
        
        discounted_price, discount_percentage = self.product_variant.get_price_with_discount(self.buyer_company)
        expected_price = Decimal('100.00')  # 120 - 20
        self.assertEqual(discounted_price, expected_price)

    def test_get_price_with_variant_discount(self):
        discount = DiscountFactory(
            seller_company=self.product_variant.product.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('25.00'),
            end_date=make_aware_datetime(datetime.now() + timedelta(days=1))
        )
        DiscountAssignmentFactory(discount=discount, content_object=self.product_variant)

class ReturnModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.return_obj = ReturnFactory()

    def test_return_creation(self):
        self.assertIsNotNone(self.return_obj.order)
        self.assertEqual(self.return_obj.status, ReturnStatuses.REQUESTED.value)

    def test_return_str_method(self):
        expected_str = f"Return for Order {self.return_obj.order.order_number}"
        self.assertEqual(str(self.return_obj), expected_str)

    def test_process_return(self):
        self.return_obj.process_return(ReturnStatuses.APPROVED.value)
        self.assertEqual(self.return_obj.status, ReturnStatuses.APPROVED.value)
        self.assertIsNotNone(self.return_obj.processed_at)

class ExchangeModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.exchange = ExchangeFactory()

    def test_exchange_creation(self):
        self.assertIsNotNone(self.exchange.original_order)
        self.assertIsNotNone(self.exchange.original_order_item)
        self.assertIsNotNone(self.exchange.new_product)
        self.assertIsNotNone(self.exchange.new_product_variant)
        self.assertEqual(self.exchange.status, ExchangeStatuses.REQUESTED.value)

    def test_exchange_str_method(self):
        expected_str = f"Exchange for Order {self.exchange.original_order.order_number}"
        self.assertEqual(str(self.exchange), expected_str)

    def test_process_exchange(self):
        self.exchange.process_exchange(ExchangeStatuses.APPROVED.value)
        self.assertEqual(self.exchange.status, ExchangeStatuses.APPROVED.value)
        self.assertIsNotNone(self.exchange.processed_at)

class DiscountModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.discount = DiscountFactory(seller_company=self.seller_company, end_date=make_aware_datetime(datetime.now() + timedelta(days=1)))

    def test_discount_creation(self):
        self.assertIsNotNone(self.discount.name)
        self.assertIn(self.discount.discount_type, [DiscountTypes.PERCENTAGE.name, DiscountTypes.AMOUNT.name])

    def test_discount_str_method(self):
        expected_str = f"{self.discount.name} - {self.discount.seller_company}"
        self.assertEqual(str(self.discount), expected_str)

    def test_is_valid(self):
        self.assertTrue(self.discount.is_valid())
        
        self.discount.is_active = False
        self.assertFalse(self.discount.is_valid())
        
        self.discount.is_active = True
        self.discount.start_date = make_aware_datetime(datetime.now() + timedelta(days=1))
        self.assertFalse(self.discount.is_valid())
        
        self.discount.start_date = make_aware_datetime(datetime.now() - timedelta(days=2))
        self.discount.end_date = make_aware_datetime(datetime.now() - timedelta(days=1))
        self.assertFalse(self.discount.is_valid())

class DiscountAssignmentModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product = ProductFactory(seller_company=self.seller_company)
        self.discount = DiscountFactory(seller_company=self.seller_company)
        self.discount_assignment = DiscountAssignmentFactory(
            discount=self.discount,
            content_object=self.product
        )

    def test_discount_assignment_creation(self):
        self.assertEqual(self.discount_assignment.discount, self.discount)
        self.assertEqual(self.discount_assignment.content_object, self.product)

    def test_discount_assignment_str_method(self):
        expected_str = f"{self.discount.name} for {self.product}"
        self.assertEqual(str(self.discount_assignment), expected_str)

class DiscountCodeModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.discount = DiscountFactory(seller_company=self.seller_company)
        self.discount_code = DiscountCodeFactory(discount=self.discount)

    def test_discount_code_creation(self):
        self.assertEqual(self.discount_code.discount, self.discount)
        self.assertIsNotNone(self.discount_code.code)

    def test_discount_code_str_method(self):
        self.assertEqual(str(self.discount_code), self.discount_code.code)

    def test_is_valid(self):
        self.assertTrue(self.discount_code.is_valid())
        
        self.discount_code.times_used = self.discount_code.max_uses
        self.assertFalse(self.discount_code.is_valid())
        
        self.discount_code.times_used = 0
        self.discount.is_active = False
        self.assertFalse(self.discount_code.is_valid())

class CalculateDiscountedPriceTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product = ProductFactory(seller_company=self.seller_company, base_price=Decimal('100.00'))
        self.price_list = PriceListFactory(seller_company=self.seller_company, is_default=True)
        self.product_price = ProductPriceFactory(price_list=self.price_list, product=self.product, price=Decimal('110.00'))
        self.buyer = BuyerCompanyFactory()

    def test_no_discount(self):
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        self.assertEqual(discounted_price, Decimal('110.00'))
        self.assertEqual(discount_percentage, Decimal('0.00'))

    def test_product_percentage_discount(self):
        discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('10.00'))
        DiscountAssignmentFactory(discount=discount, content_object=self.product)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        self.assertEqual(discounted_price, Decimal('99.00'))
        self.assertEqual(discount_percentage, Decimal('10.00'))

    def test_product_fixed_discount(self):
        discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.AMOUNT.name, value=Decimal('20.00'))
        DiscountAssignmentFactory(discount=discount, content_object=self.product)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        self.assertEqual(discounted_price, Decimal('90.00'))

    def test_customer_discount(self):
        discount = DiscountFactory(
            seller_company=self.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('15.00'),
            end_date=make_aware_datetime(datetime.now() + timedelta(days=1))
        )
        DiscountAssignmentFactory(discount=discount, content_object=self.buyer)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        self.assertEqual(discounted_price, Decimal('93.50'))
        self.assertEqual(discount_percentage, Decimal('15.00'))

    def test_multiple_combinable_discounts(self):
        product_discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('10.00'), can_be_combined=True)
        customer_discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.AMOUNT.name, value=Decimal('5.00'), can_be_combined=True)
        DiscountAssignmentFactory(discount=product_discount, content_object=self.product)
        DiscountAssignmentFactory(discount=customer_discount, content_object=self.buyer)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        expected_price = Decimal('110.00') * Decimal('0.90') - Decimal('5.00')
        self.assertEqual(discounted_price, expected_price)

    def test_multiple_non_combinable_discounts(self):
        product_discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('10.00'), can_be_combined=False)
        customer_discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.AMOUNT.name, value=Decimal('15.00'), can_be_combined=False)
        DiscountAssignmentFactory(discount=product_discount, content_object=self.product)
        DiscountAssignmentFactory(discount=customer_discount, content_object=self.buyer)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer)
        self.assertEqual(discounted_price, Decimal('95.00'))  # The better discount (fixed 15.00) should be applied

    def test_discount_code(self):
        discount = DiscountFactory(seller_company=self.seller_company, discount_type=DiscountTypes.PERCENTAGE.name, value=Decimal('20.00'))
        discount_code = DiscountCodeFactory(discount=discount)
        
        discounted_price, discount_percentage = calculate_discounted_price(Decimal('110.00'), self.product, self.buyer, discount_code=discount_code.code)
        self.assertEqual(discounted_price, Decimal('88.00'))
        self.assertEqual(discount_percentage, Decimal('20.00'))

class PriceListModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.price_list = PriceListFactory()

    def test_price_list_creation(self):
        self.assertIsNotNone(self.price_list.seller_company)
        self.assertIsNotNone(self.price_list.name)

    def test_price_list_str_method(self):
        expected_str = f"{self.price_list.name} - {self.price_list.seller_company}"
        self.assertEqual(str(self.price_list), expected_str)

    def test_default_price_list(self):
        self.price_list.is_default = True
        self.price_list.save()
        self.assertTrue(self.price_list.is_default)

        # Create another price list and set it as default
        another_price_list = PriceListFactory(seller_company=self.price_list.seller_company, is_default=True)
        self.price_list.refresh_from_db()
        
        self.assertFalse(self.price_list.is_default)
        self.assertTrue(another_price_list.is_default)

class ProductPriceModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product_price = ProductPriceFactory()

    def test_product_price_creation(self):
        self.assertIsNotNone(self.product_price.price_list)
        self.assertIsNotNone(self.product_price.product)
        self.assertIsNotNone(self.product_price.price)

    def test_product_price_str_method(self):
        expected_str = f"{self.product_price.product.name} - {self.product_price.price} ({self.product_price.price_list.name})"
        self.assertEqual(str(self.product_price), expected_str)

class WarehouseModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse = WarehouseFactory()

    def test_warehouse_creation(self):
        self.assertIsNotNone(self.warehouse.seller_company)
        self.assertIsNotNone(self.warehouse.name)
        self.assertIsNotNone(self.warehouse.address)

    def test_warehouse_str_method(self):
        expected_str = f"{self.warehouse.name} - {self.warehouse.seller_company}"
        self.assertEqual(str(self.warehouse), expected_str)

class WarehouseInventoryModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse_inventory = WarehouseInventoryFactory()

    def test_warehouse_inventory_creation(self):
        self.assertIsNotNone(self.warehouse_inventory.warehouse)
        self.assertIsNotNone(self.warehouse_inventory.product_variant)
        self.assertIsNotNone(self.warehouse_inventory.quantity)

    def test_warehouse_inventory_str_method(self):
        expected_str = f"{self.warehouse_inventory.product_variant.name} at {self.warehouse_inventory.warehouse.name}: {self.warehouse_inventory.quantity}"
        self.assertEqual(str(self.warehouse_inventory), expected_str)

class OrderCategoryModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.order_category = OrderCategoryFactory()

    def test_order_category_creation(self):
        self.assertIsNotNone(self.order_category.seller_company)
        self.assertIsNotNone(self.order_category.name)

    def test_order_category_str_method(self):
        expected_str = f"{self.order_category.name} ({self.order_category.seller_company.company.name})"
        self.assertEqual(str(self.order_category), expected_str)

class PackingSlipModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.packing_slip = PackingSlipFactory()

    def test_packing_slip_creation(self):
        self.assertIsNotNone(self.packing_slip.order)
        self.assertIsNotNone(self.packing_slip.packing_slip_number)
        self.assertIsNotNone(self.packing_slip.packed_by)
        self.assertIsNotNone(self.packing_slip.packed_at)

    def test_packing_slip_str_method(self):
        expected_str = f"Packing Slip {self.packing_slip.packing_slip_number} for Order {self.packing_slip.order.order_number}"
        self.assertEqual(str(self.packing_slip), expected_str)

class DocumentCategoryModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.document_category = DocumentCategoryFactory()

    def test_document_category_creation(self):
        self.assertIsNotNone(self.document_category.seller_company)
        self.assertIsNotNone(self.document_category.name)

    def test_document_category_str_method(self):
        self.assertEqual(str(self.document_category), f"{self.document_category.name} - {self.document_category.seller_company}")

class DocumentModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.document = DocumentFactory()

    def tearDown(self):
        super().tearDown()
        # Delete the file
        if self.document.file:
            if os.path.isfile(self.document.file.path):
                os.remove(self.document.file.path)
        
        # Delete the temporary directory and all its contents
        temp_dir = settings.MEDIA_ROOT
        for root, dirs, files in os.walk(temp_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(temp_dir)

        super().tearDown()

    def test_document_creation(self):
        self.assertIsNotNone(self.document.seller_company)
        self.assertIsNotNone(self.document.name)
        self.assertIsNotNone(self.document.file)
        self.assertIsNotNone(self.document.uploaded_by)

    def test_document_str_method(self):
        expected_str = f"{self.document.name} - {self.document.seller_company}"
        self.assertEqual(str(self.document), expected_str)


class WarehouseModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)

    def test_warehouse_creation(self):
        self.assertIsNotNone(self.warehouse.seller_company)
        self.assertIsNotNone(self.warehouse.name)
        self.assertIsNotNone(self.warehouse.address)
        self.assertIsNotNone(self.warehouse.country)
        self.assertIsNotNone(self.warehouse.city)

    def test_warehouse_str_method(self):
        expected_str = f"{self.warehouse.name} - {self.warehouse.seller_company}"
        self.assertEqual(str(self.warehouse), expected_str)

class WarehouseInventoryModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.product = ProductFactory(seller_company=self.seller_company)
        self.variant = ProductVariantFactory(product=self.product)
        self.warehouse_inventory = WarehouseInventoryFactory(
            warehouse=self.warehouse,
            product_variant=self.variant,
            quantity=10
        )

    def test_warehouse_inventory_creation(self):
        self.assertEqual(self.warehouse_inventory.warehouse, self.warehouse)
        self.assertEqual(self.warehouse_inventory.product_variant, self.variant)
        self.assertEqual(self.warehouse_inventory.quantity, 10)

    def test_warehouse_inventory_str_method(self):
        expected_str = f"{self.variant.name} at {self.warehouse.name}: 10"
        self.assertEqual(str(self.warehouse_inventory), expected_str)