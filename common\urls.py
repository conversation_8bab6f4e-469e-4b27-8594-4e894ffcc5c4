from common.views import (
    LoginView, LogoutView, RefreshTokenView, ForgotPasswordView, SetPasswordAPIView, 
    CountryViewSet, ProductReviewViewSet, PublicProductViewSet
)
from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import (
#     UserViewSet,
# )

router = DefaultRouter()

router.register(r'countries', CountryViewSet)
router.register(r'product-reviews', ProductReviewViewSet)
router.register(r'public-products', PublicProductViewSet, basename='public-products')
# router.register(r'users', UserViewSet)



urlpatterns = [
    path('auth/login/', LoginView.as_view(), name='token_obtain_pair'),
    path('auth/logout/', LogoutView.as_view(), name='logout'),
    path('auth/refreshtoken/', RefreshTokenView.as_view(), name='token_refresh'),
    path('auth/forgotpassword/', ForgotPasswordView.as_view(), name='forgotpassword'),
    path('auth/resetpassword/', SetPasswordAPIView.as_view(), name='resetpassword'),
    path('', include(router.urls)),
]