from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from dateutil.relativedelta import relativedelta

from billing.models import Price, Plan, Addon, Subscription, SubscriptionAddon, PaymentTransaction
from common.enums import PaymentMethods, PaymentPlans, SubscriptionAddOns, PaymentTransactionStatuses, PaymentTransactionTypes
from test_base.factories import (
    CompanyFactory, PriceFactory, PlanFactory, AddonFactory, SubscriptionFactory,
    SubscriptionAddonFactory, PaymentTransactionFactory, UserFactory, SellerCompanyEmployeeFactory
)
from test_base.base import BaseTestCase

from billing.models import payments_directory_path

class PriceModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.price = PriceFactory()

    def test_price_creation(self):
        self.assertIsInstance(self.price, Price)
        self.assertIsNotNone(self.price.description)
        self.assertIsNotNone(self.price.quarterly_price)
        self.assertIsNotNone(self.price.yearly_price)

    def test_price_str_method(self):
        expected_str = f"{self.price.id} - {self.price.description}"
        self.assertEqual(str(self.price), expected_str)

class PlanModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.plan = PlanFactory()

    def test_plan_creation(self):
        self.assertIsInstance(self.plan, Plan)
        self.assertIsNotNone(self.plan.name)
        self.assertIsNotNone(self.plan.description)
        self.assertIsInstance(self.plan.features, dict)
        self.assertIsNotNone(self.plan.price)

    def test_plan_str_method(self):
        self.assertEqual(str(self.plan), self.plan.name)

    def test_get_price_method(self):
        yearly_price = self.plan.get_price(PaymentPlans.YEARLY.name)
        quarterly_price = self.plan.get_price(PaymentPlans.QUARTERLY.name)
        self.assertEqual(yearly_price, self.plan.price.yearly_price)
        self.assertEqual(quarterly_price, self.plan.price.quarterly_price)

class AddonModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.addon = AddonFactory()

    def test_addon_creation(self):
        self.assertIsInstance(self.addon, Addon)
        self.assertIsNotNone(self.addon.name)
        self.assertIsNotNone(self.addon.description)
        self.assertIsNotNone(self.addon.price)
        self.assertIn(self.addon.addon_type, [choice[0] for choice in SubscriptionAddOns.choices()])

    def test_addon_str_method(self):
        expected_str = f"{self.addon.name} ({self.addon.get_addon_type_display()})"
        self.assertEqual(str(self.addon), expected_str)

class SubscriptionModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(company=self.company)

    def create_user(self, email, password):
        user = UserFactory(email=email, password=password)
        SellerCompanyEmployeeFactory(seller_company=self.seller_company, user=user)
        return user

    def test_subscription_creation(self):
        self.assertIsInstance(self.subscription, Subscription)
        self.assertIsNotNone(self.subscription.company)
        self.assertIsNotNone(self.subscription.plan)
        self.assertIn(self.subscription.payment_plan, [choice[0] for choice in PaymentPlans.choices()])
        self.assertIn(self.subscription.payment_method, [choice[0] for choice in PaymentMethods.choices()])

    def test_subscription_str_method(self):
        expected_str = f"{self.subscription.company.name} - {self.subscription.plan.name} ({self.subscription.get_payment_plan_display()})"
        self.assertEqual(str(self.subscription), expected_str)

    def test_calculate_end_date_method(self):
        self.subscription.start_date = date.today()
        self.subscription.payment_plan = PaymentPlans.YEARLY.name
        self.assertEqual(self.subscription.calculate_end_date(), date.today() + relativedelta(years=1))

        self.subscription.payment_plan = PaymentPlans.QUARTERLY.name
        self.assertEqual(self.subscription.calculate_end_date(), date.today() + relativedelta(months=3))

    def test_calculate_cost_method(self):
        plan = self.subscription.plan
        plan.price.yearly_price = 1000
        plan.price.quarterly_price = 300
        plan.price.save()

        plan.included_user_accounts = 2
        plan.save()


        # Create additional users
        for _ in range(3):
            self.create_user(f"user{_}@example.com", "password123")

        current_users = self.company.get_company_type_instance().get_employees().count()
        extra_users = current_users - 2 if current_users > 2 else 0

        # Create addon
        addon = AddonFactory()
        addon.price.yearly_price = 200
        addon.price.quarterly_price = 60
        addon.price.save()
        SubscriptionAddonFactory(subscription=self.subscription, addon=addon, quantity=2)

        # Test yearly cost
        self.subscription.payment_plan = PaymentPlans.YEARLY.name
        expected_yearly_cost = 1000 + (200 * 2)  # base +  addons
        self.assertEqual(self.subscription.calculate_cost(), expected_yearly_cost)

        # Test quarterly cost
        self.subscription.payment_plan = PaymentPlans.QUARTERLY.name
        expected_quarterly_cost = 300 + (60 * 2)  # base + addons
        self.assertEqual(self.subscription.calculate_cost(), expected_quarterly_cost)

    def test_cancel_method(self):
        self.subscription.cancel()
        self.assertFalse(self.subscription.is_active)

    def test_change_plan_method(self):
        old_plan = self.subscription.plan
        new_plan = PlanFactory()
        old_plan.price.yearly_price = 1000
        old_plan.price.quarterly_price = 300
        old_plan.price.save()
        new_plan.price.yearly_price = 2000
        new_plan.price.quarterly_price = 600
        new_plan.price.save()
        self.subscription.cost = 1000
        self.subscription.save()
        old_plan, changed_plan = self.subscription.change_plan(new_plan)

        self.assertEqual(changed_plan, new_plan)
        self.assertEqual(self.subscription.plan, new_plan)

        # Check if a new payment transaction was created for the upgrade
        latest_transaction = PaymentTransaction.objects.filter(subscription=self.subscription).order_by('-created_at').first()
        self.assertIsNotNone(latest_transaction)
        self.assertEqual(latest_transaction.transaction_type, PaymentTransactionTypes.UPGRADE.name)

    def test_change_payment_method(self):
        old_method = self.subscription.payment_method
        new_method = PaymentMethods.INVOICE.name if old_method != PaymentMethods.INVOICE.name else PaymentMethods.CREDIT_CARD.name

        old_method, changed_method = self.subscription.change_payment_method(new_method)

        self.assertEqual(changed_method, new_method)
        self.assertEqual(self.subscription.payment_method, new_method)

    def test_charge_extra_method(self):
        extra_amount = 100
        self.subscription.charge_extra(extra_amount)

        latest_transaction = PaymentTransaction.objects.filter(subscription=self.subscription).order_by('-created_at').first()
        self.assertIsNotNone(latest_transaction)
        self.assertEqual(latest_transaction.amount, Decimal(str(extra_amount)))
        self.assertEqual(latest_transaction.transaction_type, PaymentTransactionTypes.UPGRADE.name)
        self.assertEqual(latest_transaction.status, PaymentTransactionStatuses.PENDING.name)

class SubscriptionAddonModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.subscription_addon = SubscriptionAddonFactory(subscription__company=self.company)

    def test_subscription_addon_creation(self):
        self.assertIsInstance(self.subscription_addon, SubscriptionAddon)
        self.assertIsNotNone(self.subscription_addon.subscription)
        self.assertIsNotNone(self.subscription_addon.addon)
        self.assertIsNotNone(self.subscription_addon.quantity)

    def test_subscription_addon_str_method(self):
        expected_str = f"{self.subscription_addon.subscription.company.name} - {self.subscription_addon.addon.name} (x{self.subscription_addon.quantity})"
        self.assertEqual(str(self.subscription_addon), expected_str)

class PaymentTransactionModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.payment_transaction = PaymentTransactionFactory(subscription__company=self.company)

    def test_payment_transaction_creation(self):
        self.assertIsInstance(self.payment_transaction, PaymentTransaction)
        self.assertIsNotNone(self.payment_transaction.subscription)
        self.assertIsNotNone(self.payment_transaction.amount)
        self.assertIsNotNone(self.payment_transaction.payment_date)
        self.assertIn(self.payment_transaction.status, [choice[0] for choice in PaymentTransactionStatuses.choices()])
        self.assertIn(self.payment_transaction.transaction_type, [choice[0] for choice in PaymentTransactionTypes.choices()])

    def test_payment_transaction_str_method(self):
        expected_str = f"{self.payment_transaction.subscription.company.name} - {self.payment_transaction.amount} - {self.payment_transaction.get_status_display()}"
        self.assertEqual(str(self.payment_transaction), expected_str)

    def test_payment_file_upload(self):
        # This test checks if the payment_file field is correctly configured
        self.assertTrue(hasattr(self.payment_transaction, 'payment_file'))
        self.assertEqual(self.payment_transaction.payment_file.field.upload_to, payments_directory_path)
        
        # Optionally, test the function's output
        test_instance = self.payment_transaction
        test_filename = 'test.pdf'
        expected_path = f'subscriptions/payments/{test_instance.uuid}/{test_filename}'
        self.assertEqual(payments_directory_path(test_instance, test_filename), expected_path)

