# test_base.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from .factories import (
    CompanyFactory, BuyerCompanyFactory, SellerCompanyFactory, BuyerCompanyEmployeeFactory, 
    SellerCompanyEmployeeFactory, PriceListFactory, BuyerSellerRelationshipFactory
)

User = get_user_model()




class BaseTestCase(TestCase):
    def setUp(self):
        # Create base objects that might be needed across different tests
        self.b_company = CompanyFactory(name="Test Company", organisation_number="12345")
        self.s_company = CompanyFactory(name="Test Company", organisation_number="12345")
        self.company = self.s_company
        
        self.buyer_company = BuyerCompanyFactory(company=self.b_company)
        self.seller_company = SellerCompanyFactory(company=self.s_company, webshop_is_public=True)

        self.buyer_seller_relationship = BuyerSellerRelationshipFactory(buyer_company=self.buyer_company, seller_company=self.seller_company)
        
        self.buyer_user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        self.seller_user = User.objects.create_user(email="<EMAIL>", password="testpass123")

        self.buyer_employee = BuyerCompanyEmployeeFactory(
            user=self.buyer_user,
            buyer_company=self.buyer_company
        )

        self.seller_employee = SellerCompanyEmployeeFactory(
            user=self.seller_user,
            seller_company=self.seller_company
        )

        self.price_list = PriceListFactory(
            seller_company=self.seller_company,
            name="Test Price List",
            is_default=True
        )

    def tearDown(self):
        # Clean up code if needed
        pass


class BaseAPITestCase(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = self.create_user('<EMAIL>', 'testpassword')
        self.client.force_authenticate(user=self.user)

    @staticmethod
    def create_user(email, password):
        return User.objects.create_user(email=email, password=password)

    def create_and_login_user(self, email, password):
        user = self.create_user(email, password)
        self.client.force_authenticate(user=user)
        return user

    def assertSuccessResponse(self, response, status_code=status.HTTP_200_OK):
        self.assertEqual(response.status_code, status_code)
        if status_code == status.HTTP_200_OK:
            # For list views (paginated responses)
            if 'results' in response.data:
                self.assertIn('results', response.data)
                self.assertIsInstance(response.data['results'], list)
            # For detail views (non-paginated responses)
            else:
                self.assertIsInstance(response.data, dict)
        elif status_code == status.HTTP_201_CREATED:
            # For create views
            self.assertIsInstance(response.data, dict)

    def assertErrorResponse(self, response, status_code=status.HTTP_400_BAD_REQUEST):
        self.assertEqual(response.status_code, status_code)
        self.assertTrue(
            'errors' in response.data or ('swe' in response.data and 'eng' in response.data),
            "Response should contain 'errors' or both 'swe' and 'eng'"
        )

    def get_url(self, viewname, *args, **kwargs):
        return reverse(viewname, args=args, kwargs=kwargs)

    def api_get(self, url, data=None, **extra):
        return self.client.get(url, data=data, format='json', **extra)

    def api_post(self, url, data=None, **extra):
        return self.client.post(url, data=data, format='json', **extra)

    def api_put(self, url, data=None, **extra):
        return self.client.put(url, data=data, format='json', **extra)

    def api_patch(self, url, data=None, **extra):
        return self.client.patch(url, data=data, format='json', **extra)

    def api_delete(self, url, data=None, **extra):
        return self.client.delete(url, data=data, format='json', **extra)