from django.urls import reverse
from rest_framework import status
from test_base.base import BaseAPITestCase
from test_base.factories import (
    CompanyFactory, AddressFactory, SellerCompanyFactory, BuyerCompanyFactory, 
    BuyerCompanyEmployeeFactory, RoleFactory, BuyerSellerEmployeeAccessFactory,
    OrderFactory, PermissionFactory, NotificationFactory, EventLogFactory, 
    BuyerCompanySettingFactory, WarehouseFactory, ProductFactory, ProductVariantFactory,
    WarehouseInventoryFactory, OrderItemFactory
)
from seller.models import WarehouseInventory
from decimal import Decimal
from common.models import EventLog

class BuyerAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(name="Test Company", organisation_number="12345")
        self.buyer_company = BuyerCompanyFactory(company=self.company, credit_limit=1000)
        self.buyer_employee = BuyerCompanyEmployeeFactory(
            buyer_company=self.buyer_company,
            user=self.user,
        )
        self.address = AddressFactory(
            buyer_company=self.buyer_company,
            address_type='SHIPPING',
            street_address='123 Test St',
            city='Test City',
            zip_code='12345',
            country_code='SE'
        )
        self.seller_company = SellerCompanyFactory(company=CompanyFactory(name="Test Seller"))
        self.buyer_seller_access = BuyerSellerEmployeeAccessFactory(
            buyer_employee=self.buyer_employee,
            seller_company=self.seller_company,
            credit_limit=500
        )


    def test_buyer_company_list(self):
        url = reverse('buyercompany-list')
        response = self.client.get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_company_detail(self):
        url = self.get_url('buyercompany-detail', uuid=self.buyer_company.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['company']['name'], "Test Company")

    def test_buyer_company_update(self):
        url = self.get_url('buyercompany-detail', uuid=self.buyer_company.uuid)
        data = {
            'company': {
                'name': 'Updated Company',
                'organisation_number': '54321'
            },
            'credit_limit': 2000
        }
        response = self.api_patch(url, data)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['company']['name'], "Updated Company")
        self.assertEqual(response.data['credit_limit'], '2000.00')

    def test_buyer_company_employees(self):
        url = self.get_url('buyercompany-employees', uuid=self.buyer_company.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_company_settings(self):
        url = reverse('buyercompany-company-settings', kwargs={'uuid': self.buyer_company.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertIsNotNone(response.data['id'])

    def test_buyer_employee_list(self):
        url = self.get_url('buyercompanyemployee-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_employee_detail(self):
        url = self.get_url('buyercompanyemployee-detail', uuid=self.buyer_employee.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['user']['email'], self.user.email)

    def test_buyer_employee_toggle_active(self):
        url = self.get_url('buyercompanyemployee-toggle-active', uuid=self.buyer_employee.uuid)
        response = self.api_post(url)
        self.assertSuccessResponse(response)
        self.buyer_employee.refresh_from_db()
        self.assertFalse(self.buyer_employee.is_active)

    def test_buyer_employee_permissions(self):
        url = self.get_url('buyercompanyemployee-permissions', uuid=self.buyer_employee.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_address_list(self):
        url = self.get_url('address-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_address_detail(self):
        url = self.get_url('address-detail', uuid=self.address.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['street_address'], '123 Test St')

    def test_address_by_company(self):
        url = self.get_url('address-by-company')
        response = self.api_get(url, {'buyer_company_id': self.buyer_company.id})
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_order_list(self):
        OrderFactory(buyer_company=self.buyer_company)
        url = self.get_url('buyerorder-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_permission_list(self):
        PermissionFactory(codename='test_permission', name='Test Permission')
        url = self.get_url('buyerpermission-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_role_list(self):
        url = self.get_url('buyerrole-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreaterEqual(len(response.data['results']), 1) # greater because we create one in the test and one is created through a signal upon company creation

    def test_buyer_event_log_list(self):
        EventLogFactory(user=self.user, event_type='TEST_EVENT')
        url = self.get_url('buyereventlog-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreaterEqual(len(response.data['results']), 1)

    def test_buyer_notification_list(self):
        NotificationFactory(recipient=self.user, message='Test notification')
        url = self.get_url('buyernotification-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_seller_employee_access_list(self):
        url = self.get_url('buyerselleremployeeaccess-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_seller_employee_access_detail(self):
        url = self.get_url('buyerselleremployeeaccess-detail', uuid=self.buyer_seller_access.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(Decimal(response.data['credit_limit']), Decimal('500.00'))

    def test_buyer_seller_employee_access_create(self):
        new_seller = SellerCompanyFactory(company=CompanyFactory(name="New Seller"))
        url = self.get_url('buyerselleremployeeaccess-list')
        data = {
            'buyer_employee': self.buyer_employee.pk,
            'seller_company': new_seller.pk,
            'credit_limit': 300
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(Decimal(response.data['credit_limit']), Decimal('300.00'))
        self.assertEqual(response.data['buyer_employee'], self.buyer_employee.pk)
        self.assertEqual(response.data['seller_company'], new_seller.pk)



class BuyerOrderViewSetTestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.seller_company = SellerCompanyFactory()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.product = ProductFactory(seller_company=self.seller_company)
        self.variant = ProductVariantFactory(product=self.product)
        self.buyer_company = BuyerCompanyFactory()
        self.buyer_employee = BuyerCompanyEmployeeFactory(buyer_company=self.buyer_company, user=self.user)
        WarehouseInventoryFactory(warehouse=self.warehouse, product_variant=self.variant, quantity=10)

    def test_place_order(self):
        url = reverse('sellerorder-place-order')
        data = {
            'items': [
                {
                    'product': self.product.id,
                    'product_variant': self.variant.id,
                    'quantity': 2
                }
            ],
            'shipping_address': {
                'address': '123 Test St',
                'city': 'Test City',
                'zip_code': '12345',
                'country_code': 'SE'
            },
            'billing_address': {
                'address': '123 Test St',
                'city': 'Test City',
                'zip_code': '12345',
                'country_code': 'SE'
            }
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['items'][0]['warehouse'], self.warehouse.id)
        
        # Check if inventory was deducted
        warehouse_inventory = WarehouseInventory.objects.get(warehouse=self.warehouse, product_variant=self.variant)
        self.assertEqual(warehouse_inventory.quantity, 8)

    def test_place_order_insufficient_stock(self):
        url = reverse('sellerorder-place-order')
        data = {
            'items': [
                {
                    'product': self.product.id,
                    'product_variant': self.variant.id,
                    'quantity': 15
                }
            ],
            'shipping_address': {
                'address': '123 Test St',
                'city': 'Test City',
                'zip_code': '12345',
                'country_code': 'SE'
            },
            'billing_address': {
                'address': '123 Test St',
                'city': 'Test City',
                'zip_code': '12345',
                'country_code': 'SE'
            }
        }
        response = self.api_post(url, data)
        self.assertErrorResponse(response, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Insufficient stock', str(response.data['error']))

    def test_ship_items(self):
        order = OrderFactory(seller_company=self.seller_company, buyer_company=self.buyer_company)
        order_item = OrderItemFactory(order=order, product=self.product, product_variant=self.variant, quantity=2)
        
        url = reverse('sellerorder-ship-items', kwargs={'uuid': order.uuid})
        data = {
            'items': [
                {
                    'id': order_item.id,
                    'quantity': 1,
                    'warehouse_id': self.warehouse.id
                }
            ]
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response)
        
        order_item.refresh_from_db()
        self.assertEqual(order_item.shipped_quantity, 1)
        self.assertEqual(order_item.warehouse, self.warehouse)