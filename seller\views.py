from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import (
    SellerCompany, SellerCompanySetting, SellerCompanyEmployee, ProductCategory,
    Product, ProductImage, ProductVariant, Discount, DiscountAssignment,
    DiscountCode, PriceList, ProductPrice, Warehouse, WarehouseInventory,
    OrderCategory, Return, ReturnItem, Exchange, PackingSlip,
    DocumentCategory, Document, BuyerSellerRelationship, SellerOrderNote,
    VATRate, SellerBlogCategory, SellerBlogPost, SellerFAQCategory, SellerFAQItem,
    VATCategory, ProductVisibilityRule
)
from .serializers import (
    SellerCompanySerializer, SellerCompanySettingSerializer,
    SellerCompanyEmployeeSerializer, ProductCategorySerializer, ProductListSerializer,
    ProductDetailSerializer, ProductImageSerializer, ProductVariantListSerializer,
    ProductVariantDetailSerializer, DiscountSerializer, DiscountAssignmentSerializer,
    DiscountCodeSerializer, PriceListSerializer, ProductPriceSerializer, 
    WarehouseSerializer, WarehouseInventorySerializer, OrderCategorySerializer, 
    ReturnSerializer, ReturnItemSerializer, SellerExchangeSerializer, 
    SellerExchangeSerializer, PackingSlipSerializer, DocumentCategorySerializer,
    DocumentSerializer, SellerOrderListSerializer, SellerEventLogSerializer, SellerOrderDetailSerializer,
    SellerNotificationSerializer, SellerPermissionSerializer, SellerRoleSerializer,
    BuyerSellerRelationshipSerializer, SellerCompanyEmployeeInviteSerializer,
    SellerOrderNoteSerializer, InviteBuyerSerializer, ExchangeProcessSerializer,
    OrderStatusUpdateSerializer, ProcessOrderExchangeSerializer, ProcessOrderReturnSerializer,
    ReturnProcessSerializer, VATRateSerializer, SellerBlogCategorySerializer, 
    SellerBlogPostSerializer, SellerFAQCategorySerializer, SellerFAQItemSerializer,
    VATCategorySerializer, ProductVisibilityRuleSerializer
)
from django.db import transaction
from utils.base_views import BaseModelView
from common.enums import CompanyTypes, NotificationTypes, ExchangeStatuses, SubscriptionAddOns, OrderStatuses
from common.views import BaseOrderViewSet, BaseRoleViewSet, BaseEventLogViewSet, BasePermissionViewSet, BaseNotificationViewSet
from drf_yasg.utils import swagger_auto_schema
from common.models import Notification
from common.utils import make_aware_datetime
from rest_framework.exceptions import ValidationError as DRFValidationError
from datetime import datetime
from django.contrib.contenttypes.models import ContentType
from rest_framework.parsers import MultiPartParser, FormParser


class SellerCompanyViewSet(BaseModelView):
    queryset = SellerCompany.objects.all()
    serializer_class = SellerCompanySerializer
    filterset_fields = ['company__name', 'webshop_is_public']
    search_fields = ['company__name', 'domain']
    internal_endpoint = True

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        user_company = self.get_user_company(user)

        if user_company:
            # Filter based on the user's company or customer account
            company_filter = self.get_company_filter(user_company)
            if company_filter:
                queryset = queryset.filter(**company_filter)

        return queryset
    
    @action(detail=True, methods=['get'])
    def employees(self, request, uuid=None):
        seller_company = self.get_object()
        employees = SellerCompanyEmployee.objects.filter(seller_company=seller_company)
        serializer = SellerCompanyEmployeeSerializer(employees, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get', 'post'])
    def company_settings(self, request, uuid=None):
        seller_company = self.get_object()
        settings, created = SellerCompanySetting.objects.get_or_create(seller_company=seller_company)
        
        if request.method == 'POST':
            serializer = SellerCompanySettingSerializer(settings, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        
        serializer = SellerCompanySettingSerializer(settings)
        return Response(serializer.data)
    
    
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

class SellerCompanySettingViewSet(BaseModelView):
    queryset = SellerCompanySetting.objects.all()
    serializer_class = SellerCompanySettingSerializer
    filterset_fields = ['seller_company']
    internal_endpoint = True
    external_endpoint = False

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

class SellerCompanyEmployeeViewSet(BaseModelView):
    queryset = SellerCompanyEmployee.objects.all()
    serializer_class = SellerCompanyEmployeeSerializer
    filterset_fields = ['seller_company', 'user__email', 'is_active']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    internal_endpoint = True

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, uuid=None):
        employee = self.get_object()
        employee.is_active = not employee.is_active
        employee.save()
        return Response({'eng': 'Employee status updated', 'swe': 'Status uppdaterad'})

    @action(detail=True, methods=['get'])
    def permissions(self, request, uuid=None):
        employee = self.get_object()
        permissions = employee.role.permissions.all() if employee.role else []
        page = self.paginate_queryset(permissions)
        if page is not None: 
            serializer = SellerPermissionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = SellerPermissionSerializer(permissions, many=True)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)
    
    @swagger_auto_schema(
        request_body=SellerCompanyEmployeeInviteSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=False, methods=['post'])
    def invite(self, request):
        seller_company = self.get_user_company(request.user)
        current_users = seller_company.company.get_company_type_instance().get_employees().count()
        allowed_users = seller_company.company.get_company_subscription().plan.included_user_accounts
        added_user_accounts = seller_company.company.get_company_subscription().addons.filter(addon_type=SubscriptionAddOns.USER_ACCOUNT.name).count()
        if current_users >= allowed_users + added_user_accounts :
            return Response({
                'eng': 'Maximum number of users reached. Add an add-on service to be able to invite more users.', 
                'swe': 'Maximalt antal användare nådd. Lägg till en tilläggsmodul för att kunna bjuda in fler användare.'
            }, status=status.HTTP_400_BAD_REQUEST)
        serializer = SellerCompanyEmployeeInviteSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            employee = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        serializer.save()

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

class ProductCategoryViewSet(BaseModelView):
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    filterset_fields = ['seller_company', 'name', 'is_active']
    search_fields = ['name']
    internal_endpoint = True

class ProductViewSet(BaseModelView):
    queryset = Product.objects.all()
    filterset_fields = ['seller_company', 'category', 'is_active']
    search_fields = ['name', 'description', 'sku', 'ean']
    internal_endpoint = True

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductListSerializer
        return ProductDetailSerializer

    @action(detail=True, methods=['get'])
    def variants(self, request, uuid=None):
        product = self.get_object()
        variants = product.variants.all()
        page = self.paginate_queryset(variants)
        if page is not None: 
            serializer = ProductVariantDetailSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = ProductVariantDetailSerializer(variants, many=True)

        return Response(serializer.data)

class ProductImageViewSet(BaseModelView):
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    filterset_fields = ['product']
    internal_endpoint = True

    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        request_body=ProductImageSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    

    @swagger_auto_schema(
        request_body=ProductImageSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)


    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        pass 

class ProductVariantViewSet(BaseModelView):
    queryset = ProductVariant.objects.all()
    filterset_fields = ['product', 'is_active']
    search_fields = ['name', 'sku', 'ean']
    internal_endpoint = True
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ProductVariantListSerializer
        return ProductVariantDetailSerializer

class DiscountViewSet(BaseModelView):
    queryset = Discount.objects.all()
    serializer_class = DiscountSerializer
    filterset_fields = ['seller_company', 'discount_type', 'is_active']
    search_fields = ['name']
    internal_endpoint = True

class DiscountAssignmentViewSet(BaseModelView):
    queryset = DiscountAssignment.objects.all()
    serializer_class = DiscountAssignmentSerializer
    filterset_fields = ['discount']
    internal_endpoint = True

    def get_queryset(self):
        # NOTE: because the BaseModelView get_queryset needs the model to have a direct relationship to the user's company,
        # which DiscountAssignemnt does not, we must override the get_queryset method here or the queryset will be empty
        from buyer.models import BuyerCompany
        from individual_customer.models import CustomerAccount

        queryset = DiscountAssignment.objects.filter(discount__seller_company=self.get_user_company(self.request.user))
        assignment_type = self.request.query_params.get('assignment_type')
        if assignment_type:
            if assignment_type == 'PRODUCT':
                content_type = ContentType.objects.get_for_model(Product)
            elif assignment_type == 'PRODUCT_VARIANT':
                content_type = ContentType.objects.get_for_model(ProductVariant)
            elif assignment_type == 'CUSTOMER':
                content_type = ContentType.objects.get_for_model(CustomerAccount)
            elif assignment_type == 'BUYER_COMPANY':
                content_type = ContentType.objects.get_for_model(BuyerCompany)
            else:
                raise DRFValidationError({"eng": "Invalid assignment type", "swe": "Ogiltigt tilldelningsmål"})
            queryset = queryset.filter(content_type=content_type)
        return queryset

class DiscountCodeViewSet(BaseModelView):
    queryset = DiscountCode.objects.all()
    serializer_class = DiscountCodeSerializer
    filterset_fields = ['discount']
    search_fields = ['code']
    internal_endpoint = True

class PriceListViewSet(BaseModelView):
    queryset = PriceList.objects.all()
    serializer_class = PriceListSerializer
    filterset_fields = ['seller_company', 'is_default', 'is_active']
    search_fields = ['name']
    internal_endpoint = True
    external_endpoint = False

class ProductPriceViewSet(BaseModelView):
    queryset = ProductPrice.objects.all()
    serializer_class = ProductPriceSerializer
    filterset_fields = ['price_list', 'product', 'product_variant']
    internal_endpoint = True
    external_endpoint = False

class WarehouseViewSet(BaseModelView):
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    filterset_fields = ['seller_company', 'is_active']
    search_fields = ['name']
    internal_endpoint = True
    external_endpoint = False

class WarehouseInventoryViewSet(BaseModelView):
    queryset = WarehouseInventory.objects.all()
    serializer_class = WarehouseInventorySerializer
    filterset_fields = ['warehouse', 'product_variant']
    internal_endpoint = True
    external_endpoint = False

class OrderCategoryViewSet(BaseModelView):
    queryset = OrderCategory.objects.all()
    serializer_class = OrderCategorySerializer
    filterset_fields = ['seller_company']
    search_fields = ['name']
    internal_endpoint = True
    external_endpoint = False

class ReturnViewSet(BaseModelView):
    queryset = Return.objects.all()
    serializer_class = ReturnSerializer
    filterset_fields = ['order', 'status']
    search_fields = ['order__order_number']
    internal_endpoint = True

    @swagger_auto_schema(
        request_body=ReturnProcessSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def process_return(self, request, uuid=None):
        return_obj = self.get_object()
        new_status = request.data.get('status')
        try:
            return_obj.process_return(new_status)
            return Response({'eng': 'Return processed successfully', 'swe': 'Retur processad'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
        
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

class ReturnItemViewSet(BaseModelView):
    queryset = ReturnItem.objects.all()
    serializer_class = ReturnItemSerializer
    filterset_fields = ['return_request', 'order_item']
    internal_endpoint = True
    external_endpoint = False

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

class ExchangeViewSet(BaseModelView):
    queryset = Exchange.objects.all()
    serializer_class = SellerExchangeSerializer
    filterset_fields = ['id', 'status', 'original_order', 'requested_at']
    search_fields = ['original_order__order_number']
    internal_endpoint = True
    
    def get_queryset(self):
        queryset = Exchange.objects.filter(original_order__seller_company=self.get_user_company(self.request.user))
        return queryset

    @swagger_auto_schema(
        request_body=ExchangeProcessSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def process_exchange(self, request, uuid=None):
        exchange = self.get_object()
        new_status = request.data.get('status')

        with transaction.atomic():
            exchange.status = new_status
            exchange.processed_at = make_aware_datetime(datetime.now())
            exchange.save()

            # Update the associated return
            if exchange.related_return:
                exchange.related_return.status = new_status
                exchange.related_return.processed_at = exchange.processed_at
                exchange.related_return.save()

        return Response(SellerExchangeSerializer(exchange).data)
        
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

class PackingSlipViewSet(BaseModelView):
    queryset = PackingSlip.objects.all()
    serializer_class = PackingSlipSerializer
    filterset_fields = ['order']
    search_fields = ['packing_slip_number', 'order__order_number']
    internal_endpoint = True
    external_endpoint = False

class DocumentCategoryViewSet(BaseModelView):
    queryset = DocumentCategory.objects.all()
    serializer_class = DocumentCategorySerializer
    filterset_fields = ['seller_company']
    search_fields = ['name']
    internal_endpoint = True


class DocumentViewSet(BaseModelView):
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    filterset_fields = ['seller_company', 'category', 'is_active']
    search_fields = ['name']
    internal_endpoint = True
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        request_body=DocumentSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @swagger_auto_schema(
        request_body=DocumentSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def perform_create(self, serializer):
        serializer.save(uploaded_by=self.request.user.seller_employee_profile)

    def perform_update(self, serializer):
        serializer.save()


class SellerOrderViewSet(BaseOrderViewSet):
    internal_endpoint = True
    
    def get_serializer_class(self):
        if self.action == 'list':
            return SellerOrderListSerializer
        return SellerOrderDetailSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Set manual to True and created_by to the requesting user's seller_employee_profile
        serializer.validated_data['manual'] = True
        serializer.validated_data['created_by'] = request.user.seller_employee_profile

        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        order = self.get_object()
        order.update_status(OrderStatuses.CANCELLED.name)
        return Response({'eng': 'Order cancelled', 'swe': 'Order makulerad'})

    @swagger_auto_schema(
        request_body=OrderStatusUpdateSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def update_status(self, request, uuid=None):
        order = self.get_object()
        new_status = request.data.get('status')
        try:
            order.update_status(new_status)
            
            # Create notification for buyer
            Notification.objects.create(
                recipient=order.buyer_company or order.account,
                notification_type=NotificationTypes.ORDER_STATUS_CHANGED,
                title=f"Order {order.order_number} Status Updated",
                message=f"Your order status has been updated to {order.get_status_display()}",
                related_order=order
            )
            
            return Response({'eng': 'Order status updated', 'swe': 'Orderstatus uppdaterad'})
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        request_body=ProcessOrderReturnSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def process_return(self, request, uuid=None):
        order = self.get_object()
        serializer = ProcessOrderReturnSerializer(data=request.data, context={'request': request})
        
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        return_request = serializer.validated_data['return_request']
        new_status = serializer.validated_data['status']
        
        if return_request.order != order:
            return Response({'error': 'Return request does not belong to this order'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            return_request.process_return(new_status)
            
            # Create notification for buyer
            Notification.objects.create(
                recipient=order.buyer_company or order.account,
                notification_type=NotificationTypes.RETURN_STATUS_UPDATE,
                title=f"Return for Order {order.order_number} Processed",
                message=f"Your return request has been processed. New status: {return_request.get_status_display()}",
                related_order=order,
                related_return=return_request
            )
            
            return Response({'eng': 'Return processed successfully', 'swe': 'Retur processad'})
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        request_body=ProcessOrderExchangeSerializer,
        responses={200: 'OK', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def process_exchange(self, request, uuid=None):
        order = self.get_object()
        serializer = ProcessOrderExchangeSerializer(data=request.data, context={'request': request})
        
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        exchange_request = serializer.validated_data['exchange']
        new_status = serializer.validated_data['status']
        
        if exchange_request.original_order != order:
            return Response({'error': 'Exchange request does not belong to this order'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            exchange_request.process_exchange(new_status)
            
            # Create notification for buyer
            Notification.objects.create(
                recipient=order.buyer_company or order.account,
                notification_type=NotificationTypes.EXCHANGE_STATUS_UPDATE,
                title=f"Exchange for Order {order.order_number} Processed",
                message=f"Your exchange request has been processed. New status: {exchange_request.get_status_display()}",
                related_order=order,
                related_exchange=exchange_request
            )
            
            return Response({'eng': 'Exchange processed successfully', 'swe': 'Byte processad'})
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def ship_items(self, request, uuid=None):
        order = self.get_object()
        items_to_ship = request.data.get('items', [])

        for item_data in items_to_ship:
            item = order.items.get(id=item_data['id'])
            quantity = item_data['quantity']
            warehouse = Warehouse.objects.get(id=item_data['warehouse_id'])

            item.ship(quantity, warehouse)

        if order.is_fully_shipped():
            order.update_status(OrderStatuses.SHIPPED.name)

        return Response({'eng': 'Items shipped successfully', 'swe': 'Artiklarna skickades'})

class SellerPermissionViewSet(BasePermissionViewSet):
    serializer_class = SellerPermissionSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(company_types__icontains=CompanyTypes.SELLER.name)
        return queryset

class SellerRoleViewSet(BaseRoleViewSet):
    serializer_class = SellerRoleSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_company_model(self):
        return SellerCompany

    def get_company_profile_attr(self):
        return 'seller_employee_profile'

class SellerEventLogViewSet(BaseEventLogViewSet):
    serializer_class = SellerEventLogSerializer
    internal_endpoint = True
    external_endpoint = False

class SellerNotificationViewSet(BaseNotificationViewSet):
    serializer_class = SellerNotificationSerializer
    internal_endpoint = True
    external_endpoint = False

class BuyerSellerRelationshipViewSet(BaseModelView):
    queryset = BuyerSellerRelationship.objects.all()
    serializer_class = BuyerSellerRelationshipSerializer
    filterset_fields = ['buyer_company', 'seller_company', 'is_active']
    search_fields = ['buyer_company__company__name', 'seller_company__company__name', 'customer_number']
    internal_endpoint = True
    external_endpoint = False

    @swagger_auto_schema(
        request_body=InviteBuyerSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=False, methods=['post'])
    def invite_buyer(self, request, *args, **kwargs):
        # TODO: send email ( or do it in the invite_buyer method)
        serializer = InviteBuyerSerializer(data=request.data)
        if serializer.is_valid():
            relationship = BuyerSellerRelationship.invite_buyer(seller_company=self.get_user_company(request.user), buyer_company_details=serializer.validated_data)
            if relationship:
                return Response({"eng": "Buyer invited successfully", "swe": "Kunden har blivit inbjuden"}, status=status.HTTP_201_CREATED)
            else:
                return Response({"eng": "Buyer already invited", "swe": "Kunden har redan blivit inbjuden"}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SellerOrderNoteViewSet(BaseModelView):
    queryset = SellerOrderNote.objects.all()
    serializer_class = SellerOrderNoteSerializer
    filterset_fields = ['order']
    internal_endpoint = True

class VATRateViewSet(BaseModelView):
    queryset = VATRate.objects.all()
    serializer_class = VATRateSerializer
    internal_endpoint = True


class SellerBlogCategoryViewSet(BaseModelView):
    queryset = SellerBlogCategory.objects.all()
    serializer_class = SellerBlogCategorySerializer
    internal_endpoint = True

class SellerBlogPostViewSet(BaseModelView):
    queryset = SellerBlogPost.objects.all()
    serializer_class = SellerBlogPostSerializer
    internal_endpoint = True

    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        request_body=SellerBlogPostSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    

    @swagger_auto_schema(
        request_body=SellerBlogPostSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)

class SellerFAQCategoryViewSet(BaseModelView):
    queryset = SellerFAQCategory.objects.all()
    serializer_class = SellerFAQCategorySerializer
    internal_endpoint = True

class SellerFAQItemViewSet(BaseModelView):
    queryset = SellerFAQItem.objects.all()
    serializer_class = SellerFAQItemSerializer
    internal_endpoint = True

class SellerFAQItemViewSet(BaseModelView):
    queryset = SellerFAQItem.objects.all()
    serializer_class = SellerFAQItemSerializer
    internal_endpoint = True


class VATCategoryViewSet(BaseModelView):
    queryset = VATCategory.objects.all()
    serializer_class = VATCategorySerializer
    internal_endpoint = True

class ProductVisibilityRuleViewSet(BaseModelView):
    queryset = ProductVisibilityRule.objects.all()
    serializer_class = ProductVisibilityRuleSerializer
    filterset_fields = ['seller_company', 'buyer_company', 'visibility_type']
    internal_endpoint = True