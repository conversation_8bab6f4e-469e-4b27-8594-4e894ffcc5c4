from django.db import models
from common.models import Company, BaseModel, Role, Order, OrderItem
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from common.enums import ReturnStatuses, ExchangeStatuses, ReturnReasons, DiscountTypes, VisibilityTypes, WarehouseStrategyOptions
from datetime import datetime
from model_utils import FieldTracker
from django.utils.text import slugify 
from tinymce.models import HTMLField
from django.db.models import Avg
from django.core.exceptions import ValidationError


optional = {
    "null": True,
    "blank": True
}

def document_directory_path(instance, filename):
    return f'seller/documents/{instance.seller_company.id}/{filename}'

def product_image_directory_path(instance, filename):
    return f'seller/products/{instance.product.id}/{filename}'

def blog_post_image_directory_path(instance, filename):
    return f'seller/blog/{instance.seller_company.id}/{filename}'


class BuyerSellerRelationship(BaseModel):
    """
    Represents the relationship between a buyer company and a seller company.
    """
    buyer_company = models.ForeignKey('buyer.BuyerCompany', on_delete=models.CASCADE, related_name='seller_relationships')
    seller_company = models.ForeignKey('seller.SellerCompany', on_delete=models.CASCADE, related_name='buyer_relationships')
    is_active = models.BooleanField(default=True, help_text="Indicates if the relationship is currently active")
    customer_number = models.CharField(max_length=50, blank=True, help_text="Customer number assigned by the seller to the buyer")
    notes = models.TextField(blank=True, help_text="Additional notes about the relationship")
    price_list = models.ForeignKey('seller.PriceList', on_delete=models.SET_NULL, null=True, blank=True, related_name='buyer_relationships')

    def __str__(self):
        return f"{self.buyer_company} - {self.seller_company}"

    class Meta:
        unique_together = ('buyer_company', 'seller_company')
        ordering = ['-id']

    @classmethod
    def invite_buyer(cls, seller_company, buyer_company=None, buyer_company_details=None):
        from buyer.models import BuyerCompany
        
        if buyer_company:
            return cls.objects.create(buyer_company=buyer_company, seller_company=seller_company)
        elif buyer_company_details:
            company, created = Company.objects.get_or_create(
                organisation_number=buyer_company_details['organisation_number'],
                defaults={
                    'name': buyer_company_details['name'],
                    'contact_email': buyer_company_details.get('email'),
                    'contact_phone_number': buyer_company_details.get('phone_number', None),
                }
            )
            buyer_company, created = BuyerCompany.objects.get_or_create(company=company)
            if not BuyerSellerRelationship.objects.filter(buyer_company=buyer_company, seller_company=seller_company).exists():
                return cls.objects.create(buyer_company=buyer_company, seller_company=seller_company)
            else:
                return None

class SellerCompany(BaseModel):
    """
    Represents a company that acts as a seller in the system.
    """
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='seller_profile')
    webshop_is_public = models.BooleanField(default=True, help_text="Indicates if the seller's webshop is public or invite-only")
    domain = models.CharField(max_length=255, unique=True, null=True, blank=True, help_text="Custom domain for the seller's webshop")

    def __str__(self):
        return f"Seller: {self.company.name}"
    
    class Meta: 
        ordering = ['-id']
    
    def get_buyer_employees_with_access(self):
        from buyer.models import BuyerCompanyEmployee
        return BuyerCompanyEmployee.objects.filter(
            buyerselleremployeeaccess__seller_company=self,
            buyerselleremployeeaccess__is_active=True
        )
    
    def get_employees(self):
        return SellerCompanyEmployee.objects.filter(seller_company=self)

class SellerCompanySetting(BaseModel):
    """
    Stores additional settings for a seller company.
    """
    seller_company = models.OneToOneField(SellerCompany, on_delete=models.CASCADE, related_name='settings')
    reply_to_email_address = models.EmailField(**optional, help_text="Email to be used as reply-address for Amazon SES-sendouts")
    
    # Customize color and font on portal
    portal_customization = models.JSONField(default=dict, blank=True, help_text="Custom color and font settings for the portal")
    
    # Set specific stock threshold
    stock_threshold = models.PositiveIntegerField(default=10, help_text="Minimum stock level before alerts are triggered")
    
    # Set BankId rules
    bank_id_rules = models.JSONField(default=dict, blank=True, help_text="Rules for BankId authentication")
    
    # Set minimum order rule
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), validators=[MinValueValidator(Decimal('0.00'))], help_text="Minimum order amount allowed")
    
    # Set custom text for emails
    email_custom_text = models.JSONField(default=dict, blank=True, help_text="Custom text for different types of emails")
    
    # Set custom fields for checkout # TODO: add to orders
    checkout_custom_fields = models.JSONField(default=list, blank=True, help_text="Custom fields to be displayed during checkout")
    
    # Set reminders for invites and notifications for orders
    invite_reminder_days = models.PositiveIntegerField(default=7, help_text="Days after which to send a reminder for pending invites")
    order_notification_preferences = models.JSONField(default=dict, blank=True, help_text="Preferences for order notifications")

    # Allow or disallow pre-ordering
    allow_preorders = models.BooleanField(default=False, help_text="Allow orders when stock levels are insufficient")
    warehouse_strategy = models.CharField(
        max_length=20,
        choices=WarehouseStrategyOptions.choices(),
        default=WarehouseStrategyOptions.MOST_STOCK.name
    )

    # Order number prefix
    order_number_prefix = models.CharField(max_length=10, default="", help_text="Prefix for order numbers")
    # Order number suffix
    order_number_suffix = models.CharField(max_length=10, default="", help_text="Suffix for order numbers")

    def __str__(self):
        return f"Settings for {self.seller_company}"

    class Meta: 
        ordering = ['-id']

class SellerCompanyEmployee(BaseModel):
    """
    Represents an employee of a seller company.
    """
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='employees')
    user = models.OneToOneField('common.User', on_delete=models.CASCADE, related_name='seller_employee_profile')
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, related_name='seller_employees')
    is_active = models.BooleanField(default=True, help_text="Indicates if the employee is active in this role")

    class Meta:
        unique_together = ('user', 'seller_company')
        ordering = ['-id']

    def __str__(self):
        return f"{self.user} - {self.seller_company} ({self.role})"
    
    def has_perm(self, perm):
        from common.utils import has_permission
        return has_permission(self, perm)


class ProductCategory(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='product_categories')
    name = models.CharField(max_length=255)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, related_name='children', **optional)
    description = models.TextField(**optional)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name_plural = 'Product categories'
        unique_together = ('name', 'seller_company', 'parent')
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

class VATCategory(BaseModel):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name

# TODO: how do we solve the fact that there for some countries are multiple VAT rates within the same category? e.g. 12% and 6% in reduced?
# How do we know which of the two VAT rates to apply to a given order based on an articles VAT category and the orders shipping country?
class VATRate(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='vat_rates')
    country = models.ForeignKey('common.Country', on_delete=models.CASCADE, related_name='vat_rates')
    category = models.ForeignKey(VATCategory, on_delete=models.CASCADE, related_name='vat_rates')
    rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(Decimal('0.00'))])

    class Meta:
        #unique_together = ('country', 'category')
        ordering = ['-id']

    def __str__(self):
        return f"{self.country.name} - {self.category.name}: {self.rate}%"

class Product(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='products')
    name = models.CharField(max_length=255)
    description = models.TextField()
    inventory_tracking = models.BooleanField(default=False)
    sku = models.CharField(max_length=100, unique=True, **optional)
    ean = models.CharField(max_length=100, unique=True, **optional)
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, related_name='products', **optional)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], default=0)
    is_active = models.BooleanField(default=True)
    vat_category = models.ForeignKey(VATCategory, on_delete=models.PROTECT, related_name='products')
    
    # SEO fields
    meta_title = models.CharField(max_length=60, **optional)
    meta_description = models.CharField(max_length=160, **optional)
    slug = models.SlugField(max_length=255, unique=True, **optional)

    tracker = FieldTracker()

    def get_price_with_discount(self, customer=None, quantity=1, discount_code=None):
        from .utils import calculate_discounted_price
        base_price = self.get_price(customer, quantity)
        discounted_price, discount_percentage = calculate_discounted_price(base_price, self, customer, quantity, discount_code)
        return discounted_price, discount_percentage
    
    def get_vat_rate(self, country_code=None, country=None):
        if self.vat_category:
            try:
                if country_code:
                    return VATRate.objects.get(country__code=country_code, category=self.vat_category).rate # TODO: must be filtered on company
                elif country:
                    return VATRate.objects.get(country=country, category=self.vat_category).rate # TODO: must be filtered on company
            except VATRate.DoesNotExist:
                return Decimal('0.00')
        return Decimal('0.00')

    @property 
    def average_rating(self):
        return self.get_average_rating()
    
    def get_stock_level(self, warehouse=None):
        if self.inventory_tracking:
            warehouses = self.warehouses.all()
            if warehouse:
                warehouses = warehouses.filter(warehouse=warehouse)
            return sum(warehouses.values_list('quantity', flat=True))
        return float('inf')

    def get_average_rating(self):
        return self.reviews.aggregate(Avg('rating'))['rating__avg'] or Decimal('0.00')
    
    def save(self, *args, **kwargs):
        if not self.slug or 'name' in self.tracker.changed():
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} - {self.seller_company}"
    
    def get_price(self, buyer_company=None, quantity=1):
        if buyer_company:
            try:
                relationship = BuyerSellerRelationship.objects.get(buyer_company=buyer_company, seller_company=self.seller_company)
                price_list = relationship.price_list or PriceList.objects.get(seller_company=self.seller_company, is_default=True)
            except BuyerSellerRelationship.DoesNotExist:
                price_list = PriceList.objects.get(seller_company=self.seller_company, is_default=True)
        else:
            price_list = PriceList.objects.get(seller_company=self.seller_company, is_default=True)

        try:
            return ProductPrice.objects.filter(
                price_list=price_list,
                product=self,
                product_variant__isnull=True,
                min_quantity__lte=quantity
            ).order_by('-min_quantity').first().price
        except AttributeError:
            return self.base_price

    class Meta:
        ordering = ['-id']
    
    
        
    
class ProductImage(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to=product_image_directory_path)
    alt_text = models.CharField(max_length=255, **optional)
    is_primary = models.BooleanField(default=False)

    def __str__(self):
        return f"Image for {self.product.name}"
    
    class Meta: 
        ordering = ['-id']

class ProductVariant(BaseModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    sku = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    sku = models.CharField(max_length=100, unique=True, **optional)
    ean = models.CharField(max_length=100, unique=True, **optional)
    price_adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    vat_category = models.ForeignKey(VATCategory, on_delete=models.PROTECT, related_name='product_variants')

    def get_price_with_discount(self, customer=None, quantity=1, discount_code=None):
        from .utils import calculate_discounted_price
        base_price = self.get_price(customer, quantity)
        discounted_price, discount_percentage = calculate_discounted_price(base_price, self, customer, quantity, discount_code)
        return discounted_price, discount_percentage
    
    def get_vat_rate(self, country_code=None, country=None):
        if self.vat_category:
            try:
                if country_code:
                    return VATRate.objects.get(country__code=country_code, category=self.vat_category).rate
                elif country:
                    return VATRate.objects.get(country=country, category=self.vat_category).rate
            except VATRate.DoesNotExist:
                return Decimal('0.00')
        return Decimal('0.00')

    @property 
    def average_rating(self):
        return self.get_average_rating()
    
    def get_average_rating(self):
        return self.reviews.aggregate(Avg('rating'))['rating__avg'] or Decimal('0.00')
    
    def get_price(self, buyer_company=None, quantity=1):
        if buyer_company:
            try:
                relationship = BuyerSellerRelationship.objects.get(buyer_company=buyer_company, seller_company=self.product.seller_company)
                price_list = relationship.price_list or PriceList.objects.get(seller_company=self.product.seller_company, is_default=True)
            except BuyerSellerRelationship.DoesNotExist:
                price_list = PriceList.objects.get(seller_company=self.product.seller_company, is_default=True)
        else:
            price_list = PriceList.objects.get(seller_company=self.product.seller_company, is_default=True)

        try:
            variant_price = ProductPrice.objects.filter(
                price_list=price_list,
                product=self.product,
                product_variant=self,
                min_quantity__lte=quantity
            ).order_by('-min_quantity').first().price
            return variant_price
        except AttributeError:
            return self.product.get_price(buyer_company, quantity) + self.price_adjustment

    def get_stock_level(self, warehouse=None):
        if self.product.inventory_tracking:
            warehouses = self.warehouses.all()
            if warehouse:
                warehouses = warehouses.filter(warehouse=warehouse)
            return sum(warehouses.values_list('quantity', flat=True))
        return float('inf')

    def __str__(self):
        return f"{self.product.name} - {self.name}"
    
    class Meta: 
        ordering = ['-id']
    
    
    


class Discount(BaseModel):
    seller_company = models.ForeignKey('SellerCompany', on_delete=models.CASCADE, related_name='discounts')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    discount_type = models.CharField(max_length=20, choices=DiscountTypes.choices())
    value = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField(**optional)
    end_date = models.DateTimeField(**optional)
    is_active = models.BooleanField(default=True)
    can_be_combined = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.name} - {self.seller_company}"
    
    class Meta: 
        ordering = ['-id']

    def is_valid(self):
        from common.utils import make_aware_datetime
        now = make_aware_datetime(datetime.now())
        return self.is_active and self.start_date <= now <= self.end_date

class DiscountAssignment(BaseModel):
    discount = models.ForeignKey(Discount, on_delete=models.CASCADE, related_name='assignments')
    
    # This can point to either a Product, ProductVariant, or Customer
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        unique_together = ('discount', 'content_type', 'object_id')
        ordering = ['-id']

    def __str__(self):
        return f"{self.discount.name} for {self.content_object}"

class DiscountCode(BaseModel):
    discount = models.ForeignKey(Discount, on_delete=models.CASCADE, related_name='codes')
    code = models.CharField(max_length=50, unique=True)
    max_uses = models.PositiveIntegerField(null=True, blank=True)
    times_used = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.code
    
    class Meta: 
        ordering = ['-id']

    def is_valid(self):
        return self.discount.is_valid() and (self.max_uses is None or self.times_used < self.max_uses)
    
class PriceList(BaseModel):
    """
    Represents a price list that can be assigned to specific buyers or used as a default.
    """
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='price_lists')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ('seller_company', 'name')
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

    def save(self, *args, **kwargs):
        if self.is_default:
            # Ensure only one default price list per seller
            PriceList.objects.filter(seller_company=self.seller_company, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class ProductPrice(BaseModel):
    """
    Represents a price for a product or product variant in a specific price list.
    """
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, related_name='product_prices')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='prices')
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='prices', null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    min_quantity = models.PositiveIntegerField(default=1)
    
    class Meta:
        unique_together = ('price_list', 'product', 'product_variant', 'min_quantity')
        ordering = ['price_list', 'product', 'product_variant', 'min_quantity']

    def __str__(self):
        variant_str = f" - {self.product_variant.name}" if self.product_variant else ""
        return f"{self.product.name}{variant_str} - {self.price} ({self.price_list.name})"

class Warehouse(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='warehouses')
    name = models.CharField(max_length=255)
    address = models.TextField()
    country = models.ForeignKey('common.Country', on_delete=models.PROTECT)
    city = models.CharField(max_length=100)
    manager = models.ForeignKey(SellerCompanyEmployee, on_delete=models.SET_NULL, null=True, related_name='warehouses')
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)

    class Meta: 
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

class WarehouseInventory(BaseModel):
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='inventory')
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='inventory')
    quantity = models.IntegerField(default=0)
    reorder_level = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ('warehouse', 'product_variant')
        verbose_name_plural = 'Warehouse inventories'
        ordering = ['-id']

    def __str__(self):
        return f"{self.product_variant.name} at {self.warehouse.name}: {self.quantity}"


class OrderCategory(BaseModel):
    """
    Represents a category for internal order organization.
    """
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='order_categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default="#FFFFFF", help_text="HEX color code")

    class Meta:
        unique_together = ('seller_company', 'name')
        verbose_name_plural = 'Order categories'
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} ({self.seller_company.company.name})"
    


class Return(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='returns')
    exchange = models.OneToOneField('Exchange', on_delete=models.SET_NULL, related_name='related_return', **optional)
    items = models.ManyToManyField(OrderItem, through='ReturnItem')
    status = models.CharField(max_length=20, choices=ReturnStatuses.choices(), default=ReturnStatuses.REQUESTED.value)
    reason = models.CharField(max_length=20, choices=ReturnReasons.choices())
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    additional_notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return f"Return for Order {self.order.order_number}"

    def process_return(self, new_status):
        from common.utils import make_aware_datetime
        self.status = new_status
        self.processed_at = make_aware_datetime(datetime.now())
        self.save()

    def custom_get_status_display(self):
        return self.get_status_display()

class ReturnItem(BaseModel):
    return_request = models.ForeignKey(Return, on_delete=models.CASCADE)
    order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()

    class Meta: 
        ordering = ['-id']

    def __str__(self):
        return f"Return Item: {self.quantity} x {self.order_item.product.name}"

class Exchange(BaseModel):
    original_order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='exchanges')
    original_order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE, related_name='exchanges')
    new_product = models.ForeignKey(Product, on_delete=models.CASCADE)
    new_product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, **optional)
    quantity = models.PositiveIntegerField(default=1)
    status = models.CharField(max_length=20, choices=ExchangeStatuses.choices(), default=ExchangeStatuses.REQUESTED.value)
    reason = models.CharField(max_length=20, choices=ReturnReasons.choices())
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(**optional)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return f"Exchange for Order {self.original_order.order_number}"

    def process_exchange(self, new_status):
        from common.utils import make_aware_datetime
        self.status = new_status
        self.processed_at = make_aware_datetime(datetime.now())

        if new_status == ExchangeStatuses.APPROVED.value:
            self.create_new_order()

        self.save()

    def create_new_order(self):
        # Create a new order for the exchanged item
        new_order = Order.objects.create(
            seller_company=self.original_order.seller_company,
            buyer_company=self.original_order.buyer_company,
            account=self.original_order.account,
            shipping_address=self.original_order.shipping_address,
            billing_address=self.original_order.billing_address,
        )

        # Create a new order item for the exchanged product
        new_order_item = OrderItem.objects.create(
            order=new_order,
            product=self.new_product,
            product_variant=self.new_product_variant,
            quantity=self.quantity,
        )

        # Calculate price difference
        original_price = self.original_order_item.price * self.original_order_item.quantity
        if self.new_product_variant:
            new_price, discount_percentage = self.new_product_variant.get_price_with_discount(customer=self.original_order.buyer_company, quantity=self.quantity) * self.quantity
        else:
            new_price, discount_percentage = self.new_product.get_price_with_discount(customer=self.original_order.buyer_company, quantity=self.quantity) * self.quantity
        self.price_difference = new_price - original_price

        self.new_order = new_order
        self.save()

    # TODO
    def handle_price_difference(self):
        if self.price_difference > 0:
            # Customer needs to pay additional amount
            # Implement payment logic here
            pass
        elif self.price_difference < 0:
            # Refund the difference to the customer
            # Implement refund logic here
            pass

    def custom_get_status_display(self):
        return self.get_status_display()


class PackingSlip(BaseModel):
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='packing_slip')
    packing_slip_number = models.CharField(max_length=50, unique=True)
    packed_by = models.ForeignKey(SellerCompanyEmployee, on_delete=models.SET_NULL, null=True)
    packed_at = models.DateTimeField()

    class Meta: 
        ordering = ['-id']

    def __str__(self):
        return f"Packing Slip {self.packing_slip_number} for Order {self.order.order_number}"
    

class DocumentCategory(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='document_categories')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    class Meta: 
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

class Document(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='documents')
    name = models.CharField(max_length=255)
    file = models.FileField(upload_to=document_directory_path)
    is_active = models.BooleanField(default=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(SellerCompanyEmployee, on_delete=models.SET_NULL, null=True)
    category = models.ForeignKey(DocumentCategory, on_delete=models.SET_NULL, null=True)

    class Meta: 
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"
    

class SellerOrderNote(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='order_notes')
    order = models.ForeignKey('common.Order', on_delete=models.CASCADE, related_name='seller_order_notes')
    note = models.TextField()

    def __str__(self):
        return f"Note for Order {self.order.order_number} - {self.seller_company}"
    


class SellerBlogCategory(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='blog_categories')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

class SellerBlogPost(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='blog_posts')
    title = models.CharField(max_length=200)
    image = models.ImageField(upload_to=blog_post_image_directory_path, **optional)
    preamble = models.TextField()
    content = HTMLField()
    slug = models.SlugField(max_length=250, **optional)
    published_at = models.DateTimeField(**optional)
    author = models.ForeignKey(SellerCompanyEmployee, on_delete=models.CASCADE, related_name='blog_posts')
    category = models.ForeignKey(SellerBlogCategory, on_delete=models.CASCADE, related_name='blog_posts')

    tracker = FieldTracker()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        from common.utils import make_aware_datetime
        now = make_aware_datetime(datetime.now())
        if not self.slug or 'title' in self.tracker.changed():
            self.slug = slugify(self.title)
        if not self.published_at:
            self.published_at = now
        super().save(*args, **kwargs)

class SellerFAQCategory(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='faq_categories')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.seller_company}"

class SellerFAQItem(BaseModel):
    seller_company = models.ForeignKey(SellerCompany, on_delete=models.CASCADE, related_name='faq_items')
    question = models.CharField(max_length=200)
    answer = models.TextField()
    category = models.ForeignKey(SellerFAQCategory, on_delete=models.CASCADE, related_name='faq_items')
    
    class Meta:
        ordering = ['-id']
    
    def __str__(self):
        return f"{self.question} - {self.seller_company}"
    


class ProductVisibilityRule(BaseModel):
    seller_company = models.ForeignKey('SellerCompany', on_delete=models.CASCADE, related_name='visibility_rules')
    buyer_company = models.ForeignKey('buyer.BuyerCompany', on_delete=models.CASCADE, related_name='visibility_rules')
    visibility_type = models.CharField(max_length=10, choices=VisibilityTypes.choices())
    product = models.ForeignKey('Product', on_delete=models.CASCADE, **optional)
    product_variant = models.ForeignKey('ProductVariant', on_delete=models.CASCADE, **optional)
    product_category = models.ForeignKey('ProductCategory', on_delete=models.CASCADE, **optional)

    class Meta:
        unique_together = (('seller_company', 'buyer_company', 'product'),
                           ('seller_company', 'buyer_company', 'product_variant'),
                           ('seller_company', 'buyer_company', 'product_category'))

    def clean(self):
        if self.visibility_type == VisibilityTypes.PRODUCT.name and not self.product:
            raise ValidationError({"eng": "Product must be specified for product visibility rule", "swe": "Produkt måste anges fr produktvisningspolicy"})
        elif self.visibility_type == VisibilityTypes.VARIANT.name and not self.product_variant:
            raise ValidationError({"eng": "Product variant must be specified for variant visibility rule", "swe": "Produktvariant måste anges fr variantvisningspolicy"})
        elif self.visibility_type == VisibilityTypes.CATEGORY.name and not self.product_category:
            raise ValidationError({"eng": "Product category must be specified for category visibility rule", "swe": "Produktkategori måste anges fr kategori visningspolicy"})

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)