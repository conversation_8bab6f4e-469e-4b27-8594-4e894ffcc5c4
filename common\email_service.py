from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from celery import shared_task

class EmailService:
    @staticmethod
    @shared_task
    def send_password_reset_email(user, reset_link):
        subject = "Password Reset Request"
        template = "email/reset-password.html"
        context = {
            "email": user.email,
            "id": user.uuid,
            "name": f"{user.first_name} {user.last_name}".strip() or user.email,
            "url": reset_link
        }
        message = render_to_string(template, context)
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            html_message=message,
            fail_silently=False,
        )