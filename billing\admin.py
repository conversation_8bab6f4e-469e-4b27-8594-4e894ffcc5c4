from django.contrib import admin
from .models import Plan, Price, PaymentTransaction, Subscription, SubscriptionAddon, Addon, Unsubscription

@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    pass


@admin.register(Price)
class PriceAdmin(admin.ModelAdmin):
    pass


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    pass


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    pass


@admin.register(SubscriptionAddon)
class SubscriptionAddonAdmin(admin.ModelAdmin):
    pass


@admin.register(Addon)
class AddonAdmin(admin.ModelAdmin):
    pass


@admin.register(Unsubscription)
class UnsubscriptionAdmin(admin.ModelAdmin):
    pass
