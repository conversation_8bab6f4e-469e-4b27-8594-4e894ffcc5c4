from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from billing.models import Plan, PaymentTransaction, Addon, Subscription, SubscriptionAddon
from common.enums import PaymentPlans, PaymentMethods
from test_base.factories import (
    CompanyFactory, PlanFactory, PaymentTransactionFactory, AddonFactory,
    SubscriptionFactory, SubscriptionAddonFactory, SellerCompanyEmployeeFactory,
    SellerCompanyFactory, UserFactory, BuyerCompanyFactory
)
from test_base.base import BaseAPITestCase
from buyer.models import Address, BuyerCompanySetting
from seller.models import SellerCompanySetting

User = get_user_model()

class BillingAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.addon = AddonFactory()
        self.plan = PlanFactory()
        self.company = CompanyFactory()
        self.seller_company = SellerCompanyFactory(company=self.company)
        self.subscription = SubscriptionFactory(company=self.company, plan=self.plan)
        self.seller_company_employee = SellerCompanyEmployeeFactory(seller_company=self.seller_company, user=self.user)

class PlanViewSetTest(BillingAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = self.get_url('plan-list')

    def test_list_plans(self):
        response = self.api_get(self.url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_plan(self):
        url = self.get_url('plan-detail', uuid=self.plan.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.plan.id)

class PaymentTransactionViewSetTest(BillingAPITestCase):
    def setUp(self):
        super().setUp()
        self.payment = PaymentTransactionFactory(subscription=self.subscription)
        self.url = self.get_url('payment-list')

    def test_list_payments(self):
        response = self.api_get(self.url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_payment(self):
        url = self.get_url('payment-detail', uuid=self.payment.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.payment.id)

class AddonViewSetTest(BillingAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = self.get_url('addon-list')

    def test_list_addons(self):
        response = self.api_get(self.url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_addon(self):
        url = self.get_url('addon-detail', uuid=self.addon.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.addon.id)

class SubscriptionViewSetTest(BillingAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = self.get_url('subscription-list')

    def test_list_subscriptions(self):
        response = self.api_get(self.url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_subscription(self):
        url = self.get_url('subscription-detail', uuid=self.subscription.uuid)
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['id'], self.subscription.id)

    def test_request_upgrade(self):
        new_plan = PlanFactory()
        url = self.get_url('subscription-request-upgrade', uuid=self.subscription.uuid)
        data = {'new_plan_id': new_plan.id}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.assertIn('new_cost', response.data)

    def test_request_downgrade(self):
        new_plan = PlanFactory()
        url = self.get_url('subscription-request-downgrade', uuid=self.subscription.uuid)
        data = {'new_plan_id': new_plan.id}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.assertIn('new_cost', response.data)

    def test_request_cancellation(self):
        url = self.get_url('subscription-request-cancellation', uuid=self.subscription.uuid)
        response = self.api_post(url)
        self.assertSuccessResponse(response)
        self.subscription.refresh_from_db()
        self.assertFalse(self.subscription.is_active)

    def test_add_addon(self):
        addon = AddonFactory()
        url = self.get_url('subscription-add-addon', uuid=self.subscription.uuid)
        data = {'addon_id': addon.id, 'quantity': 2}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.assertIn('new_cost', response.data)
        self.assertEqual(SubscriptionAddon.objects.filter(subscription=self.subscription, addon=addon).count(), 1)

    def test_remove_addon(self):
        subscription_addon = SubscriptionAddonFactory(subscription=self.subscription)
        url = self.get_url('subscription-remove-addon', uuid=self.subscription.uuid)
        data = {'addon_id': subscription_addon.addon.id}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.assertIn('new_cost', response.data)
        self.assertEqual(SubscriptionAddon.objects.filter(subscription=self.subscription, addon=subscription_addon.addon).count(), 0)

    def test_change_payment_plan(self):
        url = self.get_url('subscription-change-payment-plan', uuid=self.subscription.uuid)
        new_payment_plan = PaymentPlans.YEARLY.name if self.subscription.payment_plan == PaymentPlans.QUARTERLY.name else PaymentPlans.QUARTERLY.name
        data = {'new_payment_plan': new_payment_plan}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.assertIn('new_cost', response.data)
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.payment_plan, new_payment_plan)

    def test_change_payment_method(self):
        url = self.get_url('subscription-change-payment-method', uuid=self.subscription.uuid)
        new_payment_method = PaymentMethods.INVOICE.name if self.subscription.payment_method == PaymentMethods.CARD.name else PaymentMethods.CARD.name
        data = {'new_payment_method': new_payment_method}
        response = self.api_post(url, data=data)
        self.assertSuccessResponse(response)
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.payment_method, new_payment_method)

    def test_invalid_plan_upgrade(self):
        url = self.get_url('subscription-request-upgrade', uuid=self.subscription.uuid)
        data = {'new_plan_id': 9999}  # Non-existent plan ID
        response = self.api_post(url, data=data)
        self.assertErrorResponse(response, status.HTTP_404_NOT_FOUND)

    def test_invalid_addon_add(self):
        url = self.get_url('subscription-add-addon', uuid=self.subscription.uuid)
        data = {'addon_id': 9999}  # Non-existent addon ID
        response = self.api_post(url, data=data)
        self.assertErrorResponse(response, status.HTTP_404_NOT_FOUND)

    def test_invalid_payment_plan_change(self):
        url = self.get_url('subscription-change-payment-plan', uuid=self.subscription.uuid)
        data = {'new_payment_plan': 'INVALID_PLAN'}
        response = self.api_post(url, data=data)
        self.assertErrorResponse(response)

    def test_invalid_payment_method_change(self):
        url = self.get_url('subscription-change-payment-method', uuid=self.subscription.uuid)
        data = {'new_payment_method': 'INVALID_METHOD'}
        response = self.api_post(url, data=data)
        self.assertErrorResponse(response)



    
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from common.models import Company
from buyer.models import BuyerCompany, BuyerCompanyEmployee
from seller.models import SellerCompany, SellerCompanyEmployee
from billing.models import Plan, Subscription

User = get_user_model()

class SignupTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.signup_url = reverse('signup')  # Make sure this matches your URL name
        
        # Create a test plan for seller signup
        self.test_plan = PlanFactory(
            name="Test Plan",
            description="Test Plan Description",
            features={"feature1": True},
            included_user_accounts=1,
            included_products=100,
            included_customers=100
        )

    def test_buyer_signup(self):
        buyer_data = {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "company_name": "Buyer Company",
            "company_type": "BUYER",
            "organisation_number": "**********",
            "vat_number": "SE**********01",
            "street_address": "123 Buyer St",
            "zip_code": "12345",
            "city": "Buyer City",
            "country_code": "SE",
            "contact_phone_number": "+***********"
        }

        response = self.client.post(self.signup_url, buyer_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check if user was created
        self.assertTrue(User.objects.filter(email=buyer_data['email']).exists())
        
        # Check if company was created
        self.assertTrue(Company.objects.filter(name=buyer_data['company_name']).exists())
        
        # Check if buyer company was created
        company = Company.objects.get(name=buyer_data['company_name'])
        self.assertTrue(BuyerCompany.objects.filter(company=company).exists())
        
        # Check if buyer company employee was created
        user = User.objects.get(email=buyer_data['email'])
        self.assertTrue(BuyerCompanyEmployee.objects.filter(user=user).exists())
        
        # Check if billing address was created
        self.assertTrue(Address.objects.filter(
            buyer_company__company=company,
            address_type="BILLING",
            street_address=buyer_data['street_address'],
            city=buyer_data['city'],
            zip_code=buyer_data['zip_code'],
            country_code=buyer_data['country_code']
        ).exists())

        # Check if buyer company setting was created
        self.assertTrue(BuyerCompanySetting.objects.filter(buyer_company__company=company).exists())

    def test_seller_signup(self):
        seller_data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "company_name": "Seller Company",
            "company_type": "SELLER",
            "organisation_number": "0987654321",
            "vat_number": "SE987654321001",
            "street_address": "456 Seller St",
            "zip_code": "54321",
            "city": "Seller City",
            "country_code": "SE",
            "contact_phone_number": "+46987654321",
            "plan_id": self.test_plan.id,
            "payment_plan": "YEARLY",
            "payment_method": "CARD"
        }

        response = self.client.post(self.signup_url, seller_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check if user was created
        self.assertTrue(User.objects.filter(email=seller_data['email']).exists())
        
        # Check if company was created
        self.assertTrue(Company.objects.filter(name=seller_data['company_name']).exists())
        
        # Check if seller company was created
        company = Company.objects.get(name=seller_data['company_name'])
        self.assertTrue(SellerCompany.objects.filter(company=company).exists())
        
        # Check if seller company employee was created
        user = User.objects.get(email=seller_data['email'])
        self.assertTrue(SellerCompanyEmployee.objects.filter(user=user).exists())
        
        # Check if subscription was created
        self.assertTrue(Subscription.objects.filter(company=company).exists())
        subscription = Subscription.objects.get(company=company)
        self.assertEqual(subscription.plan, self.test_plan)
        self.assertEqual(subscription.payment_plan, seller_data['payment_plan'])
        self.assertEqual(subscription.payment_method, seller_data['payment_method'])

        # Check if seller company setting was created
        self.assertTrue(SellerCompanySetting.objects.filter(seller_company__company=company).exists())

    def test_invalid_signup(self):
        invalid_data = {
            "email": "<EMAIL>",
            # Missing required fields
        }

        response = self.client.post(self.signup_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_seller_signup_without_plan(self):
        seller_data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "company_name": "Seller Company No Plan",
            "company_type": "SELLER",
            "organisation_number": "1122334455",
            "vat_number": "SE112233445501",
            "street_address": "789 No Plan St",
            "zip_code": "11223",
            "city": "No Plan City",
            "country_code": "SE",
            "contact_phone_number": "+46112233445"
            # Missing plan_id, payment_plan, and payment_method
        }

        response = self.client.post(self.signup_url, seller_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('eng', response.data)
        self.assertIn('swe', response.data)
        self.assertIn('Sellers must select package, payment plan, and payment method', response.data['eng'])
        self.assertIn('Säljare måste välja paket, betalningsplan och betalningsmetod', response.data['swe'])


    def test_signup_with_existing_company(self):
        # Create an existing company
        existing_company = CompanyFactory(
            name="Existing Company",
            organisation_number="1111111111"
        )

        buyer_data = {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "company_name": "Existing Company",
            "company_type": "BUYER",
            "organisation_number": "1111111111",
            "vat_number": "SE111111111001",
            "street_address": "123 Existing St",
            "zip_code": "11111",
            "city": "Existing City",
            "country_code": "SE",
            "contact_phone_number": "+46111111111"
        }

        response = self.client.post(self.signup_url, buyer_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that a new Company wasn't created
        self.assertEqual(Company.objects.count(), 1)
        
        # Check that a BuyerCompany was created for the existing Company
        self.assertTrue(BuyerCompany.objects.filter(company=existing_company).exists())

    def test_signup_with_existing_user(self):
        # Create an existing user
        existing_user = UserFactory(email="<EMAIL>")

        seller_data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "company_name": "New Seller Company",
            "company_type": "SELLER",
            "organisation_number": "2222222222",
            "vat_number": "SE222222222001",
            "street_address": "456 New St",
            "zip_code": "22222",
            "city": "New City",
            "country_code": "SE",
            "contact_phone_number": "+46222222222",
            "plan_id": self.test_plan.id,
            "payment_plan": "YEARLY",
            "payment_method": "CARD"
        }

        response = self.client.post(self.signup_url, seller_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that a new User wasn't created
        self.assertEqual(User.objects.count(), 1)
        
        # Check that a SellerCompany was created and associated with the existing User
        self.assertTrue(SellerCompanyEmployee.objects.filter(user=existing_user).exists())

    def test_signup_with_existing_buyer_company(self):
        # Create an existing buyer company
        existing_company = CompanyFactory(name="Existing Buyer Company", organisation_number="3333333333")
        BuyerCompanyFactory(company=existing_company)

        buyer_data = {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "company_name": "Existing Buyer Company",
            "company_type": "BUYER",
            "organisation_number": "3333333333",
            "vat_number": "SE333333333001",
            "street_address": "123 Existing St",
            "zip_code": "33333",
            "city": "Existing City",
            "country_code": "SE",
            "contact_phone_number": "+46333333333"
        }

        response = self.client.post(self.signup_url, buyer_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('eng', response.data)
        self.assertIn('swe', response.data)
        self.assertIn('A buyer company with this name or organisation number already exists', response.data['eng'])
        self.assertIn('Ett köparföretag med detta namn eller organisationsnummer finns redan', response.data['swe'])

    def test_signup_with_existing_seller_company(self):
        # Create an existing seller company
        existing_company = CompanyFactory(name="Existing Seller Company", organisation_number="4444444444")
        SellerCompanyFactory(company=existing_company)

        seller_data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "company_name": "Existing Seller Company",
            "company_type": "SELLER",
            "organisation_number": "4444444444",
            "vat_number": "SE444444444001",
            "street_address": "456 Existing St",
            "zip_code": "44444",
            "city": "Existing City",
            "country_code": "SE",
            "contact_phone_number": "+46444444444",
            "plan_id": self.test_plan.id,
            "payment_plan": "YEARLY",
            "payment_method": "CARD"
        }

        response = self.client.post(self.signup_url, seller_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('eng', response.data)
        self.assertIn('swe', response.data)
        self.assertIn('A seller company with this name or organisation number already exists', response.data['eng'])
        self.assertIn('Ett säljarföretag med detta namn eller organisationsnummer finns redan', response.data['swe'])