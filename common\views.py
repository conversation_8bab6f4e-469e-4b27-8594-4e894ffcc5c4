from django.contrib.auth import get_user_model, authenticate
from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.utils.encoding import smart_bytes
from django.utils.http import urlsafe_base64_encode
from rest_framework import generics, permissions, status, views
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework.exceptions import ValidationError as DRFValidationError
from drf_yasg.utils import swagger_auto_schema

from rest_framework.decorators import action
from rest_framework.response import Response
from .models.regular import (
    User, Permission, Role, Country,
    EventLog, Order, Notification, 
    ProductReview
)
from django.db.models import Q

from .serializers import (
    UserSerializer, BaseEventLogSerializer, BaseNotificationSerializer, 
    BasePermissionSerializer, BaseRoleSerializer, 
    CountrySerializer, ProductReviewSerializer, PublicProductSerializer, 
    PublicProductVariantSerializer
)
from utils.base_views import BaseModelView

from .serializers import (
    CustomTokenObtainPairSerializer,
    ForgotPasswordSerializer,
    SetPasswordSerializer
)
from config.local import FE_BASE_URL
from common.email_service import EmailService

from traceback_with_variables import format_exc

from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.permissions import AllowAny
from django.db.models import Prefetch

User = get_user_model()

class LoginView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer
    internal_endpoint = True

    def authenticate_user(self, email, password):
        user = authenticate(email=email, password=password)
        if not user:
            raise DRFValidationError({
                "swe": "Ogiltig e-postadress eller lösenord.",
                "eng": "Invalid email or password."
            }, code=status.HTTP_401_UNAUTHORIZED)

        return user

    @swagger_auto_schema(operation_description="Login to obtain token pairs", operation_summary="User login", tags=["authentication"])
    def post(self, request, *args, **kwargs):
        try:
            email = request.data.get('email')
            password = request.data.get('password')
            user = self.authenticate_user(email, password)
            
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])
                return super().post(request, *args, **kwargs)
            else:
                raise DRFValidationError(serializer.errors)
        except DRFValidationError:
            raise
        except Exception as e:
            # Log the exception
            raise DRFValidationError({
                "swe": "Ett oväntat fel inträffade. Försök igen senare.",
                "eng": "An unexpected error occurred. Please try again later.",
                "error": str(e)
            })

class LogoutView(views.APIView):
    permission_classes = [permissions.IsAuthenticated]
    internal_endpoint = True

    @swagger_auto_schema(operation_description="Logout", operation_summary="User logout", tags=["authentication"])
    def post(self, request):
        # Implement token blacklisting here
        # This is a placeholder for the actual implementation
        return Response({"detail": "Successfully logged out."}, status=status.HTTP_200_OK)

class RefreshTokenView(TokenRefreshView):
    internal_endpoint = True

    @swagger_auto_schema(operation_description="Refresh JWT token", operation_summary="Refresh JWT token", tags=["authentication"])
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

class ForgotPasswordView(generics.GenericAPIView):
    permission_classes = (permissions.AllowAny,)
    serializer_class = ForgotPasswordSerializer
    internal_endpoint = True

    @swagger_auto_schema(operation_description="Request password reset link", operation_summary="Request password reset link", tags=["authentication"])
    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            email = serializer.validated_data['email']

            user = User.objects.filter(email=email).first()
            if not user:
                raise DRFValidationError({
                    'eng': 'There is no user account registered with this email address.',
                    'swe': 'Det finns inget användarkonto registrerat på denna mejladress.'
                })

            uidb64 = urlsafe_base64_encode(smart_bytes(user.pk))
            token = PasswordResetTokenGenerator().make_token(user)
            password_reset_link = f"{FE_BASE_URL}?uidb64={uidb64}&token={token}"

            # Send email asynchronously
            EmailService.send_password_reset_email(user, password_reset_link)

            return Response({
                'eng': 'Password reset link has been sent to your email address.',
                'swe': 'En länk för att återställa lösenordet har skickats till din mejladress.'
            }, status=status.HTTP_200_OK)

        except DRFValidationError:
            raise
        except Exception as e:
            # Log the exception
            raise DRFValidationError({
                "swe": "Ett oväntat fel inträffade. Försök igen senare.",
                "eng": "An unexpected error occurred. Please try again later.",
                "error": str(e)
            })

class SetPasswordAPIView(generics.GenericAPIView):
    permission_classes = (permissions.AllowAny,)
    serializer_class = SetPasswordSerializer
    internal_endpoint = True

    @swagger_auto_schema(operation_description="Set password with password token", operation_summary="Set password with password token", tags=["authentication"])
    def post(self, request):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            uidb64 = serializer.validated_data['uidb64']
            token = serializer.validated_data['token']

            try:
                uid = force_str(urlsafe_base64_decode(uidb64))
                user = User.objects.get(pk=uid)
            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                raise DRFValidationError({
                    "eng": "The link has expired or is invalid.",
                    "swe": "Länken har utgått eller är ogiltig."
                })

            if not PasswordResetTokenGenerator().check_token(user, token):
                raise DRFValidationError({
                    "eng": "The link has expired or is invalid.",
                    "swe": "Länken har utgått eller är ogiltig."
                })

            # Create a new serializer instance with the user in the context
            serializer_with_user = self.get_serializer(
                data=serializer.validated_data,
                context={'user': user}
            )
            serializer_with_user.is_valid(raise_exception=True)
            serializer_with_user.save()

            return Response({
                'eng': 'Password set successfully.',
                'swe': 'Lösenordet har ställts in.'
            }, status=status.HTTP_200_OK)

        except DRFValidationError:
            raise
        except Exception as e:
            # Log the exception
            raise DRFValidationError({
                "swe": "Ett oväntat fel inträffade. Försök igen senare.",
                "eng": "An unexpected error occurred. Please try again later.",
                "error": str(e)
            })
        


# class UserViewSet(BaseModelView):
#     queryset = User.objects.all()
#     serializer_class = UserSerializer
#     filterset_fields = ['email', 'is_active', 'is_verified']
#     search_fields = ['email', 'first_name', 'last_name', 'full_name']
#     internal_endpoint = True

#     @swagger_auto_schema(auto_schema=None)
#     def destroy(self, request, *args, **kwargs):
#         pass 

#     @swagger_auto_schema(auto_schema=None)
#     def create(self, request, *args, **kwargs):
#         pass 

class BaseOrderViewSet(BaseModelView):
    queryset = Order.objects.all()
    filterset_fields = ['status', 'placed_at']
    search_fields = ['order_number']

    def get_queryset(self):
        from django.contrib.contenttypes.models import ContentType
        queryset = super().get_queryset()
        user = self.request.user
        if hasattr(user, 'customer_account'):
            content_type = ContentType.objects.get_for_model(user.customer_account)
            return queryset.filter(content_type=content_type, object_id=user.customer_account.id)
        elif hasattr(user, 'buyer_employee_profile'):
            return queryset.filter(buyer_company=user.buyer_employee_profile.buyer_company)
        elif hasattr(user, 'seller_employee_profile'):
            return queryset.filter(seller_company=user.seller_employee_profile.seller_company)
        
        return queryset.none()
    
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 


class BasePermissionViewSet(BaseModelView):
    queryset = Permission.objects.all()
    serializer_class = BasePermissionSerializer
    filterset_fields = ['name', 'codename']
    search_fields = ['name', 'codename', 'description']

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

class BaseRoleViewSet(BaseModelView):
    queryset = Role.objects.all()
    serializer_class = BaseRoleSerializer
    filterset_fields = ['name', 'content_type', 'object_id']
    search_fields = ['name']

    def get_company_model(self):
        raise NotImplementedError("Subclasses must implement get_company_model()")

    def get_company_profile_attr(self):
        raise NotImplementedError("Subclasses must implement get_company_profile_attr()")

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated and hasattr(user, self.get_company_profile_attr()):
            company = self.get_user_company(user)
            return Role.objects.filter(
                content_type=ContentType.objects.get_for_model(self.get_company_model()),
                object_id=company.id
            )
        return Role.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        if Role.objects.filter(
            content_type=ContentType.objects.get_for_model(self.get_company_model()),
            object_id=self.get_user_company(user).id,
            name=serializer.validated_data['name']
        ).exists():
            raise DRFValidationError({
                "eng": "Role already exists for this company.",
                "swe": "Rollen finns redan för detta företag."
            })

        if user.is_authenticated and hasattr(user, self.get_company_profile_attr()):
            company = self.get_user_company(user)
            serializer.save(
                content_type=ContentType.objects.get_for_model(self.get_company_model()),
                object_id=company.id
            )
        else:
            raise DRFValidationError({
                "eng": f"User must be authenticated and associated with a {self.get_company_model().__name__.lower()}.",
                "swe": f"Användaren måste vara autentiserad och ha ett {self.get_company_model().__name__.lower()}."
            })

    def perform_update(self, serializer):
        # The content_type and object_id should not change on update
        serializer.save()

class BaseEventLogViewSet(BaseModelView):
    queryset = EventLog.objects.all()
    serializer_class = BaseEventLogSerializer
    filterset_fields = ['user', 'event_type', 'content_type', 'object_id']
    search_fields = ['description']

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

class BaseNotificationViewSet(BaseModelView):
    queryset = Notification.objects.all()
    serializer_class = BaseNotificationSerializer
    filterset_fields = ['content_type', 'object_id', 'notification_type', 'is_read']
    search_fields = ['title', 'message']

    def get_queryset(self):
        user = self.request.user
        return Notification.objects.filter(object_id=user.id)

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, uuid=None):
        notification = self.get_object()
        notification.mark_as_read()
        return Response({'eng': 'Notification marked as read', 'swe': 'Notifikation markerad som läst'})
    
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

class CountryViewSet(BaseModelView):
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    internal_endpoint = True

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 


class ProductReviewViewSet(BaseModelView):
    queryset = ProductReview.objects.all()
    serializer_class = ProductReviewSerializer
    internal_endpoint = True

    def get_queryset(self):
        queryset = ProductReview.objects.all() # not filtering on seller_company/buyer_company since it should be available for both
        product_id = self.request.query_params.get('product')
        product_variant_id = self.request.query_params.get('product_variant')

        if product_id:
            queryset = queryset.filter(product_id=product_id)
        elif product_variant_id:
            queryset = queryset.filter(product_variant_id=product_variant_id)

        return queryset

    def perform_create(self, serializer):
        serializer.save(buyer=self.request.user.buyer_employee_profile)

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 

    @action(detail=False, methods=['GET'])
    def my_reviews(self, request):
        if hasattr(request.user, 'buyer_employee_profile'):
            reviews = ProductReview.objects.filter(buyer=request.user.buyer_employee_profile)
        else: 
            reviews = ProductReview.objects.none()
        page = self.paginate_queryset(reviews)
        if page is not None: 
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def product_reviews(self, request):
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response({"eng": "Product ID is required", "swe": "Produkt-ID krävs"}, status=status.HTTP_400_BAD_REQUEST)

        reviews = ProductReview.objects.filter(product_id=product_id)
        page = self.paginate_queryset(reviews)
        if page is not None: 
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def product_variant_reviews(self, request):
        variant_id = request.query_params.get('variant_id')
        if not variant_id:
            return Response({"eng": "Product Variant ID is required", "swe": "Produktvariant-ID krävs"}, status=status.HTTP_400_BAD_REQUEST)

        reviews = ProductReview.objects.filter(product_variant_id=variant_id)
        page = self.paginate_queryset(reviews)
        if page is not None: 
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return Response(serializer.data)
    



class PublicProductViewSet(ReadOnlyModelViewSet):
    serializer_class = PublicProductSerializer
    permission_classes = [AllowAny]
    filterset_fields = ['category', 'is_active']
    search_fields = ['name', 'description', 'sku', 'ean']
    ordering_fields = ['name', 'base_price', 'average_rating']
    lookup_field = 'uuid'

    def get_queryset(self):
        from seller.models import SellerCompany, Product, ProductVisibilityRule, BuyerSellerRelationship, ProductVariant
        seller_domain = self.request.query_params.get('domain')
        if not seller_domain:
            raise DRFValidationError({"eng": "Seller domain is required", "swe": "Säljarens domän krävs"})

        seller_company = SellerCompany.objects.filter(domain=seller_domain).first()
        if not seller_company:
            return Product.objects.none()

        user = self.request.user
        buyer_company = None
        has_access = seller_company.webshop_is_public

        if user.is_authenticated and hasattr(user, 'buyer_employee_profile'):
            buyer_company = user.buyer_employee_profile.buyer_company
            has_access = has_access or BuyerSellerRelationship.objects.filter(
                buyer_company=buyer_company,
                seller_company=seller_company,
                is_active=True
            ).exists()

        if not has_access:
            raise DRFValidationError({
                "eng": "You do not have access to this webshop.",
                "swe": "Du har inte tillgång till denna webshop."
            }, code=status.HTTP_403_FORBIDDEN)

        products = Product.objects.filter(seller_company=seller_company, is_active=True)

        # Get all products with visibility rules
        products_with_rules = ProductVisibilityRule.objects.filter(
            seller_company=seller_company
        ).values_list('product_id', 'product_variant__product_id', 'product_category__products__id')
        products_with_rules = set([item for sublist in products_with_rules for item in sublist if item])

        if buyer_company:
            # Show products with rules for this buyer and products without rules
            visible_products = ProductVisibilityRule.objects.filter(
                seller_company=seller_company,
                buyer_company=buyer_company
            ).values_list('product_id', 'product_variant__product_id', 'product_category__products__id')
            visible_products = set([item for sublist in visible_products for item in sublist if item])
            
            products = products.filter(
                Q(id__in=visible_products) |
                (~Q(id__in=products_with_rules))  # Products without any rules
            )
        else:
            # If no buyer company, show only products without rules
            products = products.exclude(id__in=products_with_rules)

        products = products.select_related(
            'seller_company',
            'vat_category'
        ).prefetch_related(
            'images',
            Prefetch('variants', queryset=ProductVariant.objects.filter(is_active=True)),
            'reviews'
        )

        return products

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context