from django.db import models
from uuid import uuid4
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from decimal import Decimal
from common.mixins import EventLogMixin

optional = {
    "null": True,
    "blank": True
}

class BaseModel(EventLogMixin):
    """
    An abstract base model providing common fields for all models.
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    id = models.BigAutoField(primary_key=True, editable=False)
    uuid = models.UUIDField(default=uuid4, editable=False, unique=True)

    class Meta:
        abstract = True
        ordering = ['-id']

class Account(BaseModel):
    """
    An abstract base model for all account types in the system.
    """
    account_name = models.Char<PERSON>ield(max_length=255, **optional, help_text="Full name of the account holder")

    class Meta:
        ordering = ['-id']
        abstract = True

    def __str__(self):
        return self.account_name
    
