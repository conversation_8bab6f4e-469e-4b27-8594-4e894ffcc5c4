from test_base.factories import (
    OrderFactory, OrderItemFactory, UserFactory, CompanyFactory,
    PermissionFactory, RoleFactory, EventLogFactory, NotificationFactory, 
    WarehouseFactory
)
from decimal import Decimal
from common.enums import OrderStatuses, EventTypes, NotificationTypes
from django.contrib.contenttypes.models import ContentType
from test_base.base import BaseTestCase

class OrderModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.order = OrderFactory(
            seller_company=self.seller_company,
            buyer_company=self.buyer_company,
            account=self.buyer_employee,
            order_number="TEST001",
            total_amount_to_pay=Decimal("100.00"),
            status=OrderStatuses.PENDING.name
        )
        self.order_item = OrderItemFactory(
            order=self.order,
            description="Test Product",
            quantity=2,
            price=Decimal("50.00"),
            vat_rate=Decimal("25.00"),
            warehouse=self.warehouse
        )

    def test_order_creation(self):
        self.assertIsNotNone(self.order.order_number)
        self.assertEqual(self.order.seller_company, self.seller_company)
        self.assertEqual(self.order.buyer_company, self.buyer_company)
        self.assertEqual(self.order.account, self.buyer_employee)
        self.assertEqual(self.order.total_amount_to_pay, self.order_item.total_amount)
        self.assertEqual(self.order.total_amount_to_pay, Decimal("100.00"))
        self.assertEqual(self.order.order_number, "TEST001")
        self.assertEqual(self.order.status, OrderStatuses.PENDING.name)

    def test_order_str_method(self):
        expected_str = f"Order TEST001 by {self.buyer_employee}"
        self.assertEqual(str(self.order), expected_str)

    def test_order_update_status(self):
        self.order.update_status(OrderStatuses.PROCESSING.name)
        self.assertEqual(self.order.status, OrderStatuses.PROCESSING.name)
        self.assertEqual(self.order.status_history.count(), 1)

    def test_is_editable(self):
        self.assertTrue(self.order.is_editable())
        self.order.update_status(OrderStatuses.SHIPPED.value)
        self.assertFalse(self.order.is_editable())

    def test_is_fully_shipped(self):
        self.assertFalse(self.order.is_fully_shipped())
        self.order_item.ship(self.order_item.quantity, warehouse=self.warehouse)
        self.assertTrue(self.order.is_fully_shipped())

    def test_custom_get_status_display(self):
        self.assertEqual(self.order.custom_get_status_display(), "Pending")
        self.order_item.ship(1, warehouse=self.warehouse)
        self.assertEqual(self.order.custom_get_status_display(), "Partially Shipped")
        self.order_item.ship(self.order_item.quantity - 1, warehouse=self.warehouse)
        self.assertEqual(self.order.custom_get_status_display(), "Fully Shipped")

    def test_get_total_shipped_quantity(self):
        self.assertEqual(self.order.get_total_shipped_quantity(), 0)
        self.order_item.ship(1, warehouse=self.warehouse)
        self.assertEqual(self.order.get_total_shipped_quantity(), 1)

class OrderItemModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.order = OrderFactory(
            seller_company=self.seller_company,
            buyer_company=self.buyer_company,
            account=self.buyer_employee,
            order_number="TEST001",
            total_amount_to_pay=Decimal("100.00"),
            status=OrderStatuses.PENDING.name
        )
        self.order_item = OrderItemFactory(
            order=self.order,
            description="Test Product",
            quantity=2,
            price=Decimal("50.00"),
            vat_rate=Decimal("25.00"),
            warehouse=self.warehouse
        )

    def test_order_item_creation(self):
        self.assertIsNotNone(self.order_item.order)
        self.assertEqual(self.order_item.quantity, 2)
        self.assertEqual(self.order_item.price, Decimal("50.00"))
        self.assertEqual(self.order_item.vat_rate, Decimal("25.00"))

    def test_order_item_total_calculation(self):
        expected_total = Decimal("100.00")
        self.assertEqual(self.order_item.total_amount, expected_total)

    def test_is_fully_shipped(self):
        self.assertFalse(self.order_item.is_fully_shipped())
        self.order_item.ship(self.order_item.quantity, warehouse=self.warehouse)
        self.assertTrue(self.order_item.is_fully_shipped())

    def test_ship(self):
        initial_quantity = self.order_item.quantity
        self.order_item.ship(1, warehouse=self.warehouse)
        self.assertEqual(self.order_item.shipped_quantity, 1, self.order_item.warehouse)
        with self.assertRaises(ValueError):
            self.order_item.ship(initial_quantity, warehouse=self.warehouse)

    def test_ship_with_warehouse(self):
        self.order_item.ship(1, warehouse=self.warehouse)
        self.assertEqual(self.order_item.shipped_quantity, 1, self.order_item.warehouse)
        self.assertEqual(self.order_item.warehouse, self.warehouse)
        self.assertIsNone(self.order_item.shipped_at)

    def test_ship_full_quantity(self):
        self.order_item.ship(2, warehouse=self.warehouse)
        self.assertEqual(self.order_item.shipped_quantity, 2, self.order_item.warehouse)
        self.assertIsNotNone(self.order_item.shipped_at)
        self.assertTrue(self.order_item.is_fully_shipped())

class UserModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    def test_user_creation(self):
        self.assertIsNotNone(self.user.email)
        self.assertTrue(self.user.check_password('password'))

    def test_user_str_method(self):
        expected_str = f"{self.user.email} - {self.user.first_name}"
        self.assertEqual(str(self.user), expected_str)

class CompanyModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()

    def test_company_creation(self):
        self.assertIsNotNone(self.company.name)
        self.assertIsNotNone(self.company.organisation_number)

    def test_company_str_method(self):
        self.assertEqual(str(self.company), self.company.name)

    def test_company_save_method(self):
        self.company.name = "Test Company"
        self.company.save()
        self.company.refresh_from_db()
        self.assertEqual(self.company.identifier, "test_company")

class PermissionModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.permission = PermissionFactory(
            company_types=['SELLER'],
            name="Test Permission",
            codename="test_permission"
        )

    def test_permission_creation(self):
        self.assertIsNotNone(self.permission.name)
        self.assertIsNotNone(self.permission.codename)
        self.assertIsNotNone(self.permission.company_types)
        self.assertEqual(self.permission.company_types, ['SELLER'])
        self.assertEqual(self.permission.name, "Test Permission")
        self.assertEqual(self.permission.codename, "test_permission")

    def test_permission_str_method(self):
        self.assertEqual(str(self.permission), self.permission.name)

    def test_can_be_used_by(self):
        self.assertTrue(self.permission.can_be_used_by('SELLER'))
        self.assertFalse(self.permission.can_be_used_by('BUYER'))
        self.permission.company_types = ['BUYER']
        self.permission.save()
        self.permission.refresh_from_db()
        self.assertFalse(self.permission.can_be_used_by('SELLER'))
        self.assertTrue(self.permission.can_be_used_by('BUYER'))


class RoleModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.role = RoleFactory()
        self.permission = PermissionFactory(
            name="Test Permission",
            codename="test_permission",
            company_types=['SELLER']
        )
        self.role.permissions.add(self.permission)
        self.seller_employee.role = self.role
        self.seller_employee.save()

    def test_role_creation(self):
        self.assertIsNotNone(self.role.name)

    def test_role_str_method(self):
        expected_str = f"{self.role.name} - {self.role.company}"
        self.assertEqual(str(self.role), expected_str)

    def test_role_permissions(self):
        self.assertIn(self.permission, self.role.permissions.all())

    def test_has_permission(self):
        self.assertFalse(self.buyer_employee.has_perm('test_permission'))
        self.assertTrue(self.seller_employee.has_perm('test_permission'))

class EventLogModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.event_log = EventLogFactory()

    def test_event_log_creation(self):
        self.assertIsNotNone(self.event_log.user)
        self.assertIn(self.event_log.event_type, [choice[0] for choice in EventTypes.choices()])

    def test_event_log_str_method(self):
        expected_str = f"{self.event_log.event_type} - {self.event_log.content_type} - {self.event_log.user}"
        self.assertEqual(str(self.event_log), expected_str)

class NotificationModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.notification = NotificationFactory()

    def test_notification_creation(self):
        self.assertIsNotNone(self.notification.title)
        self.assertIsNotNone(self.notification.message)

    def test_notification_str_method(self):
        expected_str = f"{self.notification.get_notification_type_display()} for {self.notification.recipient}"
        self.assertEqual(str(self.notification), expected_str)

    def test_mark_as_read(self):
        self.assertFalse(self.notification.is_read)
        self.notification.mark_as_read()
        self.assertTrue(self.notification.is_read)
        self.assertIsNotNone(self.notification.read_at)

