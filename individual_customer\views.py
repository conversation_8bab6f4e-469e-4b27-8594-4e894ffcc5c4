from django.shortcuts import render
from common.views import BaseOrderViewSet, BaseModelView, BaseNotificationViewSet, BaseEventLogViewSet
from .serializers import CustomerOrderSerializer, CustomerAccountSerializer, CustomerEventLogSerializer, CustomerNotificationSerializer
from .models import CustomerAccount
from common.models import Order, EventLog, Notification



class CustomerOrderViewSet(BaseOrderViewSet):
    serializer_class = CustomerOrderSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        return queryset.filter(object_id=user.customer_account.id)

class CustomerAccountViewSet(BaseModelView):
    queryset = CustomerAccount.objects.all()
    serializer_class = CustomerAccountSerializer
    filterset_fields = ['user']
    search_fields = ['user__email', 'account_name']
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        user = self.request.user
        return CustomerAccount.objects.filter(user=user)
    
    def check_object_permissions(self, request, obj):
        return obj.user == request.user


class CustomerEventLogViewSet(BaseEventLogViewSet):
    serializer_class = CustomerEventLogSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        user = self.request.user
        return EventLog.objects.filter(object_id=user.customer_account.id)

class CustomerNotificationViewSet(BaseNotificationViewSet):
    serializer_class = CustomerNotificationSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        user = self.request.user
        return Notification.objects.filter(object_id=user.customer_account.id)


