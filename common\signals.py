from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.contenttypes.models import ContentType
from common.models import Notification, Order
from seller.models import Return, Exchange
from common.enums import NotificationTypes

@receiver(post_save, sender=Order)
def create_order_notification(sender, instance, created, **kwargs):
    if not created:  # Only for updates, not new orders
        content_type = ContentType.objects.get_for_model(instance.buyer_company or instance.account)
        Notification.objects.create(
            content_type=content_type,
            object_id=instance.buyer_company.id if instance.buyer_company else instance.account.id,
            notification_type=NotificationTypes.ORDER_STATUS_UPDATE.value,
            title=f"Order {instance.order_number} Status Update",
            message=f"Your order status has been updated to {instance.custom_get_status_display()}.",
            related_order=instance
        )

@receiver(post_save, sender=Return)
def create_return_notification(sender, instance, created, **kwargs):
    if not created:  # Only for updates, not new returns
        content_type = ContentType.objects.get_for_model(instance.order.buyer_company or instance.order.account)
        Notification.objects.create(
            content_type=content_type,
            object_id=instance.order.buyer_company.id if instance.order.buyer_company else instance.order.account.id,
            notification_type=NotificationTypes.RETURN_STATUS_UPDATE.value,
            title=f"Return for Order {instance.order.order_number} Status Update",
            message=f"Your return request status has been updated to {instance.custom_get_status_display()}.",
            related_order=instance.order,
            related_return=instance
        )

@receiver(post_save, sender=Exchange)
def create_exchange_notification(sender, instance, created, **kwargs):
    if not created:  # Only for updates, not new exchanges
        content_type = ContentType.objects.get_for_model(instance.original_order.buyer_company or instance.original_order.account)
        Notification.objects.create(
            content_type=content_type,
            object_id=instance.original_order.buyer_company.id if instance.original_order.buyer_company else instance.original_order.account.id,
            notification_type=NotificationTypes.EXCHANGE_STATUS_UPDATE.value,
            title=f"Exchange for Order {instance.original_order.order_number} Status Update",
            message=f"Your exchange request status has been updated to {instance.custom_get_status_display()}.",
            related_order=instance.original_order,
            related_exchange=instance
        )