from drf_yasg.generators import OpenAPISchemaGenerator
from drf_yasg.inspectors.view import <PERSON>I<PERSON>pect<PERSON>
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from collections import OrderedDict, defaultdict
from rest_framework.permissions import IsAuthenticated

# Custom Schema Generator with a class attribute for endpoint filtering


class BaseFilteredSchemaGenerator(OpenAPISchemaGenerator):
    endpoint_filter = None

    def get_endpoints(self, request):
        endpoints = super().get_endpoints(request)
        enumerator = self.endpoint_enumerator_class(self._gen.patterns, self._gen.urlconf, request=request)
        endpoints = enumerator.get_api_endpoints()

        view_paths = defaultdict(list)
        view_cls = {}

        for path, method, callback in endpoints:
            try:
                view = self.create_view(callback, method, request)
                if self.endpoint_filter == 'internal_endpoint': 
                    if not hasattr(view, 'internal_endpoint') or not view.internal_endpoint:
                        continue
                if self.endpoint_filter == 'external_endpoint':
                    if not hasattr(view, 'external_endpoint') or not view.external_endpoint:
                        continue

                path = self.coerce_path(path, view)
                view_paths[path].append((method, view))
                view_cls[path] = callback.cls
            except AttributeError:
                # Skip this endpoint if we can't create a view for it
                continue

        return {path: (view_cls[path], methods) for path, methods in view_paths.items()}

class internalFilteredSchemaGenerator(BaseFilteredSchemaGenerator):
    endpoint_filter = 'internal_endpoint'

class externalFilteredSchemaGenerator(BaseFilteredSchemaGenerator):
    endpoint_filter = 'external_endpoint'

   
# Factory function to create schema views
def create_schema_view(info, filter_name):
    # Set the class attribute for filtering
    if filter_name == 'internal_endpoint':
        FilteredSchemaGenerator = internalFilteredSchemaGenerator
    elif filter_name == 'external_endpoint':
        FilteredSchemaGenerator = externalFilteredSchemaGenerator
    FilteredSchemaGenerator.endpoint_filter = filter_name
    return get_schema_view(
        info,
        public=True,
        permission_classes=(IsAuthenticated,),
        generator_class=FilteredSchemaGenerator
    )

# Instantiate schema views with specific filters
schema_view_internal = create_schema_view(
    openapi.Info(
        title="internal API",
        default_version='v1',
        description="API documentation for internal users",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    'internal_endpoint'
)

schema_view_external = create_schema_view(
    openapi.Info(
        title="external API",
        default_version='v1',
        description="API documentation for external users",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    'external_endpoint'
)
