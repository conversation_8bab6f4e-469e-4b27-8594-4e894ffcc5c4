from rest_framework import serializers
from .models import (
    SellerCompany, SellerCompanySetting, SellerCompanyEmployee, ProductCategory,
    Product, ProductImage, ProductVariant, Discount, DiscountAssignment,
    DiscountCode, PriceList, ProductPrice, Warehouse, WarehouseInventory,
    OrderCategory, Return, ReturnItem, Exchange, PackingSlip,
    DocumentCategory, Document, BuyerSellerRelationship, SellerOrderNote,
    VATRate, SellerBlogPost, SellerBlogCategory, SellerFAQCategory, SellerFAQItem, 
    VATCategory, ProductVisibilityRule
)
from buyer.models import BuyerCompany, BuyerCompanyEmployee
from individual_customer.models import CustomerAccount
from common.models import User, Role, OrderItem, Country
from utils.base_serializers import BaseModelSerializer, SellerCompanyRestrictedPrimaryKeyRelatedField
from common.serializers import (
    BaseOrderListSerializer, BaseOrderDetailSerializer, BaseOrderItemSerializer, BaseEventLogSerializer,
    BaseNotificationSerializer, BasePermissionSerializer, BaseRoleSerializer,
    BaseCompanySerializer, NestedUserSerializer, UserSerializerWithoutEmail,
    BaseOrderItemSerializer, CountrySerializer,
    ProductReviewSummarySerializer
)
from common.enums import OrderStatuses, ExchangeStatuses, ReturnStatuses
from common.mixins import CompanyRoleSerializerMixin
from django.db import transaction
from django.contrib.contenttypes.models import ContentType
from rest_framework.exceptions import ValidationError as DRFValidationError
import bleach
from bs4 import BeautifulSoup


class CurrentSellerCompanyDefault:
    requires_context = True

    def __call__(self, serializer_field):
        request = serializer_field.context.get('request')
        if request and hasattr(request, 'user'):
            seller_employee = SellerCompanyEmployee.objects.filter(user=request.user).first()
            if seller_employee:
                return seller_employee.seller_company
        raise serializers.ValidationError({"eng": "User is not associated with a seller company", "swe": "Användaren är inte associerad med säljfretag"})

class CurrentSellerCompanyEmployeeDefault:
    requires_context = True

    def __call__(self, serializer_field):
        request = serializer_field.context.get('request')
        if request and hasattr(request, 'user'):
            seller_employee = SellerCompanyEmployee.objects.filter(user=request.user).first()
            if seller_employee:
                return seller_employee
        raise serializers.ValidationError({"eng": "User is not associated with a seller company", "swe": "Användaren är inte associerad med säljfretag"})

class SellerCompanySerializer(BaseModelSerializer):
    company = BaseCompanySerializer()

    class Meta:
        model = SellerCompany
        exclude = ['created_at', 'updated_at']

    def update(self, instance, validated_data):
        company_data = validated_data.pop('company')
        company_instance = instance.company

        # Update Company instance
        for attr, value in company_data.items():
            setattr(company_instance, attr, value)
        company_instance.save()

        # Update BuyerCompany instance
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance

class SellerCompanySettingSerializer(BaseModelSerializer):
    class Meta:
        model = SellerCompanySetting
        fields = '__all__'

class SellerRoleSerializer(CompanyRoleSerializerMixin, BaseRoleSerializer):
    class Meta(BaseRoleSerializer.Meta):
        fields = ['id', 'uuid', 'created_at', 'updated_at', 'name', 'permissions']
        read_only_fields = ['content_type', 'object_id']

class SellerPermissionSerializer(BasePermissionSerializer): 
    class Meta(BasePermissionSerializer.Meta): 
        fields = BasePermissionSerializer.Meta.fields

class SellerCompanyEmployeeSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    user = NestedUserSerializer()
    role = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Role.objects.all())

    custom_fields = {
        "role": SellerRoleSerializer
    }

    class Meta:
        model = SellerCompanyEmployee
        fields = '__all__'

    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', None)
        if user_data:
            user = instance.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()
        return super().update(instance, validated_data)
    
class ProductCategorySerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    parent = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=ProductCategory.objects.all(), allow_null=True)

    class Meta:
        model = ProductCategory
        fields = '__all__'
    


class PriceListSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = PriceList
        fields = '__all__'

class ProductPriceSerializer(BaseModelSerializer):
    class Meta:
        model = ProductPrice
        fields = '__all__'

class ProductImageSerializer(BaseModelSerializer):
    image = serializers.ImageField(required=True)
    image_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProductImage
        fields = '__all__'

    def get_image_url(self, obj):    
        if obj.image:
            request = self.context.get('request')
            if request is not None:
                return request.build_absolute_uri(obj.image.url)
        return None

class VATCategorySerializer(BaseModelSerializer):
    class Meta:
        model = VATCategory
        fields = '__all__'


class ProductVariantListSerializer(BaseModelSerializer):
    vat_category = serializers.PrimaryKeyRelatedField(queryset=VATCategory.objects.all())
    class Meta:
        model = ProductVariant
        fields = ['id', 'uuid', 'name', 'sku', 'is_active', 'vat_category']

class ProductVariantDetailSerializer(BaseModelSerializer):
    vat_category = serializers.PrimaryKeyRelatedField(queryset=VATCategory.objects.all())
    reviews = serializers.SerializerMethodField()
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2, read_only=True)
    stock = serializers.SerializerMethodField()

    custom_fields = {
        'vat_category': VATCategorySerializer
    }
    class Meta:
        model = ProductVariant
        fields = '__all__'

    def get_reviews(self, obj):
        reviews = obj.reviews.all().order_by('-created_at')[:5]  # Get the 5 most recent reviews
        return ProductReviewSummarySerializer(reviews, many=True).data

    def get_stock(self, obj):
        return obj.get_stock_level()

class ProductListSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    vat_category = serializers.PrimaryKeyRelatedField(queryset=VATCategory.objects.all())

    class Meta:
        model = Product
        fields = ['id', 'uuid', 'name', 'sku', 'base_price', 'is_active', 'seller_company', 'vat_category']

class ProductDetailSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantDetailSerializer(many=True, read_only=True)
    price_list = PriceListSerializer(read_only=True)
    price = ProductPriceSerializer(read_only=True)
    vat_category = serializers.PrimaryKeyRelatedField(queryset=VATCategory.objects.all())
    reviews = serializers.SerializerMethodField()
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2, read_only=True)
    stock = serializers.SerializerMethodField()
   
    custom_fields = {
        'vat_category': VATCategorySerializer
    }

    class Meta:
        model = Product
        fields = '__all__'

    def get_reviews(self, obj):
        reviews = obj.reviews.all().order_by('-created_at')[:5]  # Get the 5 most recent reviews
        return ProductReviewSummarySerializer(reviews, many=True).data

    def get_stock(self, obj):
        return obj.get_stock_level()

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['average_rating'] = instance.get_average_rating()
        return representation


class DiscountSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = Discount
        fields = '__all__'

    def validate(self, data):
        if 'start_date' in data and 'end_date' in data:
            if data['start_date'] > data['end_date']:
                raise DRFValidationError({"eng": "Start date must be before end date", "swe": "Startdatumet måste vara före slutdatumet"})
        if 'start_date' in data and self.context['request'].method in ['PUT', 'PATCH']:
            if self.instance.start_date != data['start_date']:
                raise DRFValidationError({"eng": "Start date cannot be changed", "swe": "Startdatumet kan inte ändras"})
        return data

class DiscountAssignmentSerializer(BaseModelSerializer):
    assignment_type = serializers.ChoiceField(choices=['PRODUCT', 'PRODUCT_VARIANT', 'CUSTOMER', 'BUYER_COMPANY'], write_only=True)
    assignment_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = DiscountAssignment
        fields = ['id', 'uuid', 'discount', 'assignment_type', 'assignment_id']
        # read_only_fields = ['content_type', 'object_id']

    def get_model_for_assignment_type(self, assignment_type):
        if assignment_type == 'PRODUCT':
            return Product
        elif assignment_type == 'PRODUCT_VARIANT':
            return ProductVariant
        elif assignment_type == 'CUSTOMER':
            return CustomerAccount
        elif assignment_type == 'BUYER_COMPANY':
            return BuyerCompany
        else:
            raise DRFValidationError({"eng": "Invalid assignment type", "swe": "Ogiltig tilldelningstyp"})

    def validate(self, data):
        assignment_type = data.pop('assignment_type', None)
        assignment_id = data.pop('assignment_id', None)

        if assignment_type and assignment_id:
            model = self.get_model_for_assignment_type(assignment_type)
            content_type = ContentType.objects.get_for_model(model)

            try:
                obj = model.objects.get(id=assignment_id)
            except model.DoesNotExist:
                raise DRFValidationError({
                    "eng": f"{assignment_type} with id {assignment_id} does not exist",
                    "swe": f"{assignment_type} med id {assignment_id} finns inte"
                })

            # Check for existing assignment
            if self.context['request'].method == 'POST':
                if DiscountAssignment.objects.filter(
                    discount=data['discount'],
                    content_type=content_type,
                    object_id=assignment_id
                ).exists():
                    raise DRFValidationError({
                        "eng": "This discount is already assigned to this object",
                        "swe": "Denna rabatten är redan tilldelad till detta objekt"
                    })

            data['content_type'] = content_type
            data['object_id'] = assignment_id

        return data

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        model_name = instance.content_type.model.upper()
        representation['assignment_type'] = model_name
        representation['assignment_id'] = instance.object_id
        representation['discount'] = DiscountSerializer(instance.discount).data
        return representation
    

class DiscountCodeSerializer(BaseModelSerializer):
    class Meta:
        model = DiscountCode
        fields = '__all__'


class WarehouseSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    manager = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=SellerCompanyEmployee.objects.all())

    custom_fields = {
        'manager': SellerCompanyEmployeeSerializer
    }

    class Meta:
        model = Warehouse
        fields = '__all__'

class WarehouseInventorySerializer(BaseModelSerializer):
    class Meta:
        model = WarehouseInventory
        fields = '__all__'

class OrderCategorySerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = OrderCategory
        fields = '__all__'

class ReturnItemSerializer(BaseModelSerializer):
    order_item = BaseOrderItemSerializer(read_only=True)
    order_item_id = serializers.PrimaryKeyRelatedField(
        queryset=OrderItem.objects.all(),
        source='order_item',
        write_only=True
    )

    class Meta:
        model = ReturnItem
        fields = ['id', 'order_item', 'order_item_id', 'quantity']

class ReturnSerializer(BaseModelSerializer):
    items = ReturnItemSerializer(many=True)

    class Meta:
        model = Return
        fields = ['id', 'uuid', 'order', 'status', 'reason', 'items', 'requested_at']
        read_only_fields = ['id', 'uuid', 'status', 'requested_at']

    def validate(self, data):
        order = self.context['order']
        items_data = data['items']
        
        for item_data in items_data:
            order_item = item_data['order_item']
            if order_item.order != order:
                raise serializers.ValidationError({"eng": f"Order item {order_item.id} does not belong to the given order.", "swe": f"Orderrad {order_item.id} tillhör inte den angivna ordern."})
            if item_data['quantity'] > order_item.quantity:
                raise serializers.ValidationError({"eng": f"Return quantity for item {order_item.id} exceeds the original order quantity.", "swe": f"Returnerad kvantitet för orderrad {order_item.id} överskrider originalordern."})
        
        return data

    def create(self, validated_data):
        items_data = validated_data.pop('items')
        return_request = Return.objects.create(**validated_data)
        for item_data in items_data:
            ReturnItem.objects.create(return_request=return_request, **item_data)
        return return_request


    

class PackingSlipSerializer(BaseModelSerializer):
    class Meta:
        model = PackingSlip
        fields = '__all__'

class DocumentCategorySerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = DocumentCategory
        fields = '__all__'

class DocumentSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    file = serializers.FileField(required=False, allow_null=True, use_url=True)
    file_url = serializers.SerializerMethodField(read_only=True)
    uploaded_by = SellerCompanyEmployeeSerializer(read_only=True)

    read_only_fields = ['id', 'seller_company', 'file_url']

    custom_fields = {
        'category': DocumentCategorySerializer
    }

    class Meta:
        model = Document
        fields = '__all__'

    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request is not None:
                return request.build_absolute_uri(obj.file.url)
        return None

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if not representation.get('file'):
            representation.pop('file', None)
        return representation

class SellerOrderItemSerializer(BaseOrderItemSerializer):
    product = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Product.objects.all(), required=True)
    product_variant = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    description = serializers.CharField(required=False)
    discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    discount_percentage = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    vat_rate = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    total_vat = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    warehouse = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Warehouse.objects.all(), required=False)
    
    custom_fields = {
        'product': ProductListSerializer,
        'product_variant': ProductVariantListSerializer,
        'warehouse': WarehouseSerializer
    }

    class Meta(BaseOrderItemSerializer.Meta):
        fields = BaseOrderItemSerializer.Meta.fields + ['discount_percentage', 'discount_amount', 'vat_rate', 'total_vat', 'product', 'product_variant', 'warehouse']
        read_only_fields = BaseOrderItemSerializer.Meta.read_only_fields + ['price']


class SellerOrderListSerializer(BaseOrderListSerializer):
    pass

class SellerOrderDetailSerializer(BaseOrderDetailSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    assignment_type = serializers.ChoiceField(choices=['BUYER_COMPANY', 'BUYER_COMPANY_EMPLOYEE', 'INDIVIDUAL_CUSTOMER'], write_only=True)
    assignment_id = serializers.IntegerField(write_only=True)
    discount_code = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=DiscountCode.objects.all(), required=False)
    items = SellerOrderItemSerializer(many=True)
    shipping_address = serializers.JSONField(required=True)
    billing_address = serializers.JSONField(required=True)
    
    custom_fields = {
        'discount_code': DiscountCodeSerializer,
        'created_by': SellerCompanyEmployeeSerializer
    }
    
    class Meta(BaseOrderDetailSerializer.Meta):
        fields = BaseOrderDetailSerializer.Meta.fields + [
            'assignment_type', 'assignment_id', 'category', 'shipping_address', 'billing_address', 'discount_code',
            'manual', 'created_by'
        ]
        read_only_fields = BaseOrderDetailSerializer.Meta.read_only_fields + ['manual', 'created_by']

    def control_address(self, address):
        if not isinstance(address, dict):
            raise serializers.ValidationError({"eng": "Address is not a dictionary", "swe": "Adressen är inte en dictionary"})
        if 'country_code' not in address or not address['country_code']:
            raise serializers.ValidationError({"eng": "Country code is required", "swe": "Landskod är obligatorisk"})
        if 'street_address' not in address or not address['street_address']:
            raise serializers.ValidationError({"eng": "Street address is required", "swe": "Gatuadress är obligatorisk"})
        if 'city' not in address or not address['city']:
            raise serializers.ValidationError({"eng": "City is required", "swe": "Stad är obligatorisk"})
        if 'zip_code' not in address or not address['zip_code']:
            raise serializers.ValidationError({"eng": "Zip code is required", "swe": "Postnummer är obligatorisk"})

    def validate_shipping_address(self, value):
        self.control_address(value)
        return value
    
    def validate_billing_address(self, value):
        self.control_address(value)
        return value

    def get_model_for_assignment_type(self, assignment_type):
        if assignment_type == 'BUYER_COMPANY':
            return BuyerCompany
        elif assignment_type == 'BUYER_COMPANY_EMPLOYEE':
            return BuyerCompanyEmployee
        elif assignment_type == 'INDIVIDUAL_CUSTOMER':
            return CustomerAccount
        else:
            raise DRFValidationError({"eng": "Invalid assignment type", "swe": "Ogiltig tilldelningstyp"})

    def validate(self, data):
        assignment_type = data.pop('assignment_type', None)
        assignment_id = data.pop('assignment_id', None)

        if assignment_type and assignment_id:
            model = self.get_model_for_assignment_type(assignment_type)
            content_type = ContentType.objects.get_for_model(model)

            try:
                obj = model.objects.get(id=assignment_id)
            except model.DoesNotExist:
                raise DRFValidationError({"eng": f"{assignment_type} with id {assignment_id} does not exist", "swe": f"{assignment_type} med id {assignment_id} finns inte"})

            data['content_type'] = content_type
            data['object_id'] = assignment_id

        return data
            
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        model_name = instance.content_type.model.upper()
        representation['assignment_type'] = model_name
        representation['assignment_id'] = instance.object_id
        representation['items'] = SellerOrderItemSerializer(instance.items.all(), many=True).data
        
        return representation
    
class ExchangeProcessSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=ExchangeStatuses.choices())

class ReturnProcessSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=ReturnStatuses.choices())

class ProcessOrderExchangeSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=ExchangeStatuses.choices())
    exchange = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Exchange.objects.all())

class ProcessOrderReturnSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=ReturnStatuses.choices())
    return_request = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Return.objects.all())

class OrderStatusUpdateSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=OrderStatuses.choices())

class SellerExchangeSerializer(BaseModelSerializer):
    original_order_item = SellerOrderItemSerializer(read_only=True)
    original_order = SellerOrderListSerializer(read_only=True)
    new_product = ProductListSerializer(read_only=True)
    new_product_variant = ProductVariantListSerializer(read_only=True)

    class Meta:
        model = Exchange
        fields = ['id', 'uuid', 'original_order', 'original_order_item', 'new_product', 'new_product_variant', 'quantity', 'reason', 'status', 'requested_at']
        read_only_fields = ['id', 'original_order', 'original_order_item', 'new_product', 'new_product_variant', 'quantity', 'reason', 'requested_at']

class SellerNotificationSerializer(BaseNotificationSerializer): 
    class Meta(BaseNotificationSerializer.Meta): 
        fields = BaseNotificationSerializer.Meta.fields

class SellerEventLogSerializer(BaseEventLogSerializer): 
    class Meta(BaseEventLogSerializer.Meta): 
        fields = BaseEventLogSerializer.Meta.fields

class BuyerSellerRelationshipSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = BuyerSellerRelationship
        fields = '__all__'


    # to_internal_value is performed before the Model's validation, making sure we return a serializer error rather 
    # than a django DB error. 
    def to_internal_value(self, data):
        # First, do the basic field validation
        try:
            validated_data = super().to_internal_value(data)
        except serializers.ValidationError as exc:
            raise DRFValidationError(exc.detail)
        
        if self.context['request'].method == 'POST':
            seller_company = validated_data['seller_company']
            if BuyerSellerRelationship.objects.filter(seller_company=seller_company, buyer_company=validated_data['buyer_company']).exists():
                raise serializers.ValidationError({
                    "eng": "There is already a connection between your company and this buyer company", 
                    "swe": "Det finns redan en anslutning mellan ditt företag och detta köparföretag"
                })
        return validated_data



class InviteBuyerSerializer(serializers.Serializer):
    organisation_number = serializers.CharField(max_length=12)
    name = serializers.CharField(max_length=100)
    email = serializers.EmailField()
    phone_number = serializers.CharField(max_length=20, required=False)






class SellerOrderNoteSerializer(BaseModelSerializer):
    class Meta:
        model = SellerOrderNote
        fields = '__all__'


class SellerCompanyEmployeeInviteSerializer(serializers.ModelSerializer):
    user = UserSerializerWithoutEmail(required=False)
    role = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Role.objects.all())
    email = serializers.EmailField(write_only=True)

    class Meta:
        model = SellerCompanyEmployee
        fields = ['user', 'role', 'email', 'is_active']
        read_only_fields = ['is_active']

    def validate_email(self, value):
        seller_company = self.context['request'].user.seller_employee_profile.seller_company
        if SellerCompanyEmployee.objects.filter(user__email=value, seller_company=seller_company).exists():
            raise serializers.ValidationError("An employee with this email already exists in your company.")
        return value

    def create(self, validated_data):
        with transaction.atomic():
            email = validated_data.pop('email')
            role = validated_data.pop('role')
            user_data = validated_data.pop('user', {})

            user, created = User.objects.get_or_create(email=email)
            
            if user_data:
                for key, value in user_data.items():
                    setattr(user, key, value)
                user.save()

            if created:
                user.set_unusable_password()
                user.save()

            seller_company = self.context['request'].user.seller_employee_profile.seller_company
            employee = SellerCompanyEmployee.objects.create(
                user=user,
                seller_company=seller_company,
                role=role,
                is_active=True
            )
            return employee
        

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['user']['email'] = instance.user.email
        representation['seller_company'] =  instance.seller_company.id
        return representation


class VATRateSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    category = serializers.PrimaryKeyRelatedField(queryset=VATCategory.objects.all())
    country = serializers.PrimaryKeyRelatedField(queryset=Country.objects.all())

    custom_fields = {
        'category': VATCategorySerializer,
        'country': CountrySerializer
    }
    class Meta:
        model = VATRate
        fields = '__all__'


class SellerBlogCategorySerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    class Meta:
        model = SellerBlogCategory
        fields = '__all__'

class SellerBlogPostSerializer(BaseModelSerializer):
    category = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=SellerBlogCategory.objects.all())
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    author = serializers.HiddenField(default=CurrentSellerCompanyEmployeeDefault())
    image = serializers.ImageField(required=False)
    image_url = serializers.SerializerMethodField(read_only=True)

    custom_fields = {
        'category': SellerBlogCategorySerializer,
        'author': SellerCompanyEmployeeSerializer
    }

    def to_internal_value(self, data):
        # Create a mutable copy of the data
        mutable_data = data.copy() if hasattr(data, 'copy') else data

        # Clean the content if it exists
        if 'content' in mutable_data:
            mutable_data['content'] = self.clean_content(mutable_data['content'])

        return super().to_internal_value(mutable_data)

    def clean_content(self, content):
        # List of allowed tags and attributes
        allowed_tags = ['b', 'i', 'u', 'strong', 'em', 'p', 'ul', 'ol', 'li', 'br']
        allowed_attrs = {}

        # First, use BeautifulSoup to parse the HTML
        soup = BeautifulSoup(content, 'html.parser')

        # Remove disallowed tags and their content
        for tag in soup.find_all():
            if tag.name not in allowed_tags:
                tag.decompose()

        # Convert the soup back to a string
        cleaned_content = str(soup)

        # Use bleach for an extra layer of cleaning and to ensure all tags are properly closed
        final_cleaned_content = bleach.clean(cleaned_content, tags=allowed_tags, attributes=allowed_attrs, strip=True)

        return final_cleaned_content
    
    def get_image_url(self, obj):    
        if obj.image:
            request = self.context.get('request')
            if request is not None:
                return request.build_absolute_uri(obj.image.url)
        return None
    
    class Meta:
        model = SellerBlogPost
        fields = '__all__'

class SellerFAQCategorySerializer(BaseModelSerializer): 
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())

    class Meta: 
        model = SellerFAQCategory
        fields = '__all__'


class SellerFAQItemSerializer(BaseModelSerializer): 
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    category = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=SellerFAQCategory.objects.all())

    class Meta: 
        model = SellerFAQItem
        fields = '__all__'


class ProductVisibilityRuleSerializer(BaseModelSerializer):
    seller_company = serializers.HiddenField(default=CurrentSellerCompanyDefault())
    product = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=Product.objects.all(), required=False, allow_null=True)
    product_variant = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False, allow_null=True)
    product_category = SellerCompanyRestrictedPrimaryKeyRelatedField(queryset=ProductCategory.objects.all(), required=False, allow_null=True)

    custom_fields = {
        'product': ProductListSerializer,
        'product_variant': ProductVariantListSerializer,
        'product_category': ProductCategorySerializer,
        'buyer_company': 'buyer.serializers.BuyerCompanySerializer'
    }
    class Meta:
        model = ProductVisibilityRule
        fields = '__all__'

    def validate(self, data):
        visibility_type = data.get('visibility_type')
        product = data.get('product')
        product_variant = data.get('product_variant')
        product_category = data.get('product_category')

        if visibility_type == 'PRODUCT':
            if not product:
                raise DRFValidationError({"eng": "Product is required for product visibility rule", "swe": "Produkten är obligatorisk för produktsynlighetsregeln"})
            data['product_variant'] = None
            data['product_category'] = None
        elif visibility_type == 'VARIANT':
            if not product_variant:
                raise DRFValidationError({"eng": "Product variant is required for variant visibility rule", "swe": "Produktvarianten är obligatorisk för variantensynlighetsregeln"})
            data['product'] = None
            data['product_category'] = None
        elif visibility_type == 'CATEGORY':
            if not product_category:
                raise DRFValidationError({"eng": "Product category is required for category visibility rule", "swe": "Produktkategorin är obligatorisk för kategorisynlighetsregeln"})
            data['product'] = None
            data['product_variant'] = None
        else:
            raise DRFValidationError({"eng": "Invalid visibility type", "swe": "Ogiltig synlighetsregel"})

        return data