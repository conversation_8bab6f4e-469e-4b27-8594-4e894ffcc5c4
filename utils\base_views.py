from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.exceptions import ValidationError as DRFValidationError
from django_filters import FilterSet
from django.db import models
from django.shortcuts import get_object_or_404
import django_filters
from rest_framework.views import get_view_description as drf_get_view_description
from django.contrib.contenttypes.models import ContentType

class BaseFilterSet(FilterSet):
    class Meta:
        filter_overrides = {
            models.CharField: {
                'filter_class': django_filters.CharFilter,
                'extra': lambda f: {
                    'lookup_expr': 'icontains',
                },
            },
            models.BooleanField: {
                'filter_class': django_filters.BooleanFilter,
            },
            models.DateTimeField: {
                'filter_class': django_filters.IsoDateTimeFilter,
            },
        }

    @classmethod
    def get_filters(cls):
        filters = super().get_filters()
        model = getattr(cls._meta, 'model', None)
        
        if model:
            for field_name, field in model._meta.fields_map.items():
                if field_name not in filters:
                    filter_class = cls._meta.filter_overrides.get(type(field), {}).get('filter_class', django_filters.Filter)
                    filters[field_name] = filter_class(field_name=field_name)
        
        return filters

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self._meta.model:
            return  # Exit early if there's no model
        
        for field_name, field in self._meta.model._meta.fields_map.items():
            if field_name not in self.filters:
                filter_class = self._meta.filter_overrides.get(type(field), {}).get('filter_class', django_filters.Filter)
                self.filters[field_name] = filter_class(field_name=field_name)



class BaseModelView(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    ordering_fields = '__all__'
    # filterset_class = BaseFilterSet
    lookup_field = 'uuid'
    internal_endpoint = False
    external_endpoint = False

    @classmethod
    def get_view_description(cls, html=False):
        return drf_get_view_description(cls, html=html)
    
    def get_filterset_class(self):
        if not hasattr(self, 'filterset_class'):
            class AutoFilterSet(BaseFilterSet):
                class Meta(BaseFilterSet.Meta):
                    model = self.queryset.model
                    fields = '__all__'
            self.filterset_class = AutoFilterSet
        return self.filterset_class

    def get_user_company(self, user):
        if hasattr(user, 'seller_employee_profile'):
            return user.seller_employee_profile.seller_company
        elif hasattr(user, 'buyer_employee_profile'):
            return user.buyer_employee_profile.buyer_company
        elif hasattr(user, 'customer_account'):
            return user.customer_account
        return None

    def get_queryset(self):
        # NOTE: because this BaseModelView class's get_queryset needs the model to have a direct relationship to the user's company,
        # which some models do not, the views for those models must override this get_queryset method (see for example DiscountAssignmentViewset)
        queryset = super().get_queryset()
        if not hasattr(queryset.model, 'seller_company') and not hasattr(queryset.model, 'buyer_company') and not \
            hasattr(queryset.model, 'company') and not hasattr(queryset.model, 'content_type') and not hasattr(queryset.model, 'object_id') and not \
                hasattr(queryset.model, 'user') and not hasattr(queryset.model, 'buyer_employee_profile') and not \
                    hasattr(queryset.model, 'seller_employee_profile') and not hasattr(queryset.model, 'seller_employee') and not \
                        hasattr(queryset.model, 'buyer_employee') and not hasattr(queryset.model, 'customer_account'):
                            return queryset
        user = self.request.user
        if hasattr(queryset.model, 'user'):
            queryset = queryset.filter(user=user)
        else:
            if not hasattr(user, 'customer_account'):
                user_company = self.get_user_company(user)
                if user_company:
                    company_filter = self.get_company_filter(user_company)
                    if company_filter:
                        queryset = queryset.filter(**company_filter)
                    else: 
                        queryset = queryset.none()
                else: 
                    queryset = queryset.none()
            else:
                queryset = queryset.filter(content_type=ContentType.objects.get_for_model(user.customer_account), object_id=user.customer_account.id)

        return queryset
    
    def get_object(self):
        queryset = self.get_queryset()
        obj = get_object_or_404(queryset, uuid=self.kwargs['uuid'])
        return obj

    def get_company_filter(self, user_company):
        model = self.queryset.model
        user = self.request.user

        if model.__name__ == 'Role':
            content_type = ContentType.objects.get_for_model(user_company)
            return {
                'content_type': content_type,
                'object_id': user_company.id
            }

        if hasattr(user, 'buyer_employee_profile'):
            if hasattr(model, 'buyer_company'):
                return {'buyer_company': user_company}
            elif hasattr(model, 'company'):
                return {'company': user_company.company if hasattr(user_company, 'company') else user_company}
            elif hasattr(model, 'content_type') and hasattr(model, 'object_id'):
                content_type = ContentType.objects.get_for_model(user_company)
                return {'content_type': content_type, 'object_id': user_company.id}
        elif hasattr(user, 'seller_employee_profile'):
            if hasattr(model, 'seller_company'):
                return {'seller_company': user_company}
            elif hasattr(model, 'company'):
                return {'company': user_company.company if hasattr(user_company, 'company') else user_company}
            elif hasattr(model, 'content_type') and hasattr(model, 'object_id'):
                content_type = ContentType.objects.get_for_model(user_company)
                return {'content_type': content_type, 'object_id': user_company.id}
        elif hasattr(user, 'customer_account'):
            if hasattr(model, 'content_type') and hasattr(model, 'object_id'):
                content_type = ContentType.objects.get_for_model(user_company)
                return {'content_type': content_type, 'object_id': user_company.id}
            elif hasattr(model, 'company'):
                return {'company': user_company}
            elif hasattr(model, 'content_type') and hasattr(model, 'object_id'):
                content_type = ContentType.objects.get_for_model(user_company)
                return {'content_type': content_type, 'object_id': user_company.id}

        return None
    
    def get_object_company(self, obj):
        user = self.request.user

        if hasattr(obj, 'buyer_employee') and hasattr(user, 'buyer_employee_profile'):
            return obj.buyer_employee.buyer_company
        elif hasattr(obj, 'seller_company') and hasattr(user, 'seller_employee_profile'):
            return obj.seller_company
        elif hasattr(obj, 'buyer_company'):
            return obj.buyer_company
        elif hasattr(obj, 'seller_company'):
            return obj.seller_company
        elif hasattr(obj, 'company'):
            return obj.company
        elif hasattr(obj, 'content_type') and hasattr(obj, 'object_id'):
            model = obj.content_type.model_class()
            return model.objects.filter(id=obj.object_id).first()
        return None

    def check_object_permissions(self, request, obj):
        super().check_object_permissions(request, obj)
        if not hasattr(request.user, 'customer_account'):
            user_company = self.get_user_company(request.user)
            if user_company:
                obj_company = self.get_object_company(obj)
                if obj_company != user_company:
                    if hasattr(user_company, 'company') and user_company.company == obj_company: 
                        pass 
                    else:
                        raise DRFValidationError({"eng": "You do not have permission to access this object", "swe": "Du har inte behörighet att komma åt detta objekt"})
        else:
            if obj.content_type != ContentType.objects.get_for_model(request.user.customer_account) or obj.object_id != request.user.customer_account.id:
                raise DRFValidationError({"eng": "You do not have permission to access this object", "swe": "Du har inte behörighet att komma åt detta objekt"})

    def perform_create(self, serializer):
        if not hasattr(self.request.user, 'customer_account'):
            user_company = self.get_user_company(self.request.user)
            if user_company:
                company_field = self.get_company_field(serializer.Meta.model)
                if company_field:
                    serializer.save(**{company_field: user_company})
                else:
                    serializer.save()
        else:
            serializer.save()

    def get_company_field(self, model):
        if hasattr(model, 'seller_company'):
            return 'seller_company'
        elif hasattr(model, 'buyer_company'):
            return 'buyer_company'
        elif hasattr(model, 'company'):
            return 'company'
        return None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update({'request': self.request})
        return context
    
    def get_serializer(self, *args, **kwargs):
        serializer_class = self.get_serializer_class()
        kwargs.setdefault('context', self.get_serializer_context())
        
        if self.request.method == 'PATCH':
            kwargs['partial'] = True
        
        return serializer_class(*args, **kwargs)

    def create(self, request):
        try:
            serializer = self.serializer_class(data=request.data, context={'request': request})
            if serializer.is_valid():
                self.perform_create(serializer)
                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
            else:
                raise DRFValidationError(serializer.errors)
        except Exception: 
            raise
    
    def update(self, request, *args, **kwargs):
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.serializer_class(instance, data=request.data, context={'request': request}, partial=partial)
            if serializer.is_valid():
                self.perform_update(serializer)
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                raise DRFValidationError(serializer.errors)
        except Exception: 
            raise

    def partial_update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)