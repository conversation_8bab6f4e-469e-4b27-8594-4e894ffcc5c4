from django.db import models
from common.models import BaseModel
from ckeditor_uploader.fields import RichTextUploadingField
from django.utils.text import slugify 
from config.local import BE_BASE_URL
from urllib.parse import urljoin

optional = {
    "null": True,
    "blank": True
}

def guide_image_directory_path(instance, filename):
    # will be uploaded to MEDIA_ROOT/users/<id>/<filename>
    return f"guides/{filename}"

def videos_directory_path(instance, filename):
    # will be uploaded to MEDIA_ROOT/videos/<filename>
    return f"videos/{filename}"

def thumbnail_diretory_path(instance, filename): 
    return f"videos/thumbnails/{filename}"

def blog_image_directory_path(instance, filename):
    # will be uploaded to MEDIA_ROOT/blogs/<id>/<filename>
    return f"blogs/{filename}"

def testimonial_image_directory_path(instance, filename):
    # will be uploaded to MEDIA_ROOT/testimonials/<filename>
    return f"testimonials/{filename}"

class Guide(BaseModel):
    eng_subject = models.CharField(max_length=255)
    swe_subject = models.CharField(max_length=255)
    eng_text = RichTextUploadingField(**optional)
    swe_text = RichTextUploadingField(**optional)
    image = models.ImageField(upload_to=guide_image_directory_path, **optional)
    shown = models.BooleanField(default=True)
    display_to_seller = models.BooleanField(default=False)
    display_to_buyer = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.eng_subject
    
    class Meta: 
        ordering = ['-id']
    

class FAQCategory(BaseModel): 
    swe_category = models.CharField(max_length=255)
    eng_category = models.CharField(max_length=255)
    display_to_seller = models.BooleanField(default=False)
    display_to_buyer = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "FAQ Categories"
        ordering = ['-id']

    def __str__(self): 
        return f"{self.eng_category} - {self.swe_category}"

class FAQ(BaseModel): 
    category = models.ForeignKey(FAQCategory, related_name="faq", on_delete=models.SET_NULL, null=True, blank=True)
    swe_question = models.CharField(max_length=255)
    eng_question = models.CharField(max_length=255)
    swe_answer = models.TextField()
    eng_answer = models.TextField()
    shown = models.BooleanField(default=True)
    landing_page = models.BooleanField(default=False)
    display_to_seller = models.BooleanField(default=False)
    display_to_buyer = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "FAQs"
        ordering = ['-id']

    def __str__(self): 
        return self.eng_question


class BlogCategory(BaseModel): 
    swe_category = models.CharField(max_length=255)
    eng_category = models.CharField(max_length=255)

    class Meta:
        verbose_name_plural = "Blog Categories"
        ordering = ['-id']

    def __str__(self): 
        return f"{self.eng_category} - {self.swe_category}"

class BlogPost(BaseModel): 
    swe_title = models.CharField(max_length=255)
    eng_title = models.CharField(max_length=255)
    category = models.ForeignKey(BlogCategory, related_name="blogs", on_delete=models.PROTECT)
    image = models.ImageField(upload_to=blog_image_directory_path, blank=True, null=True)
    swe_preamble = models.TextField()
    eng_preamble = models.TextField()
    eng_text = RichTextUploadingField(**optional)
    swe_text = RichTextUploadingField(**optional)
    published = models.BooleanField(default=True)
    featured = models.BooleanField(default=False)
    published_at = models.DateField(**optional)
    slug = models.SlugField(unique=True, max_length=155, **optional)

    def save(self, *args, **kwargs):
        self.slug = slugify(self.swe_title)
        super(BlogPost, self).save(*args, **kwargs)

    class Meta:
        ordering = ['-published_at']

    def __str__(self): 
        return self.eng_title
    
class BlogComments(BaseModel): 
    blog = models.ForeignKey(BlogPost, related_name="comments", on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField()
    comment = models.TextField()
    shown = models.BooleanField(default=True)

    class Meta: 
        ordering = ['-id']
        verbose_name_plural = "Blog Comments"

    def __str__(self): 
        return self.name
    

class Testimonial(BaseModel): 
    title = models.CharField(max_length=255, **optional)
    company = models.CharField(max_length=255, **optional)
    name = models.CharField(max_length=255)
    review = models.TextField()
    image = models.ImageField(upload_to=testimonial_image_directory_path)
    shown = models.BooleanField(default=True)
    
    class Meta: 
        ordering = ['-id']
        
    def __str__(self): 
        return self.name
    

class HelpText(BaseModel): 
    model_name = models.CharField(max_length=255, **optional)
    model_field = models.CharField(max_length=255, **optional)
    eng = models.CharField(max_length=255)
    swe = models.CharField(max_length=255)
    shown = models.BooleanField(default=True)

    class Meta: 
        ordering = ['-id']

    def __str__(self): 
        return self.eng


class InformationText(BaseModel): 
    vue_page = models.CharField(max_length=255, **optional)
    vue_english_header = models.CharField(max_length=255, **optional)
    eng = models.TextField()
    swe = models.TextField()
    shown = models.BooleanField(default=True)

    class Meta: 
        ordering = ['-id']

    def __str__(self): 
        return self.eng
    

class VideoGuide(BaseModel): 
    eng_title = models.CharField(max_length=155, **optional)
    swe_title = models.CharField(max_length=155, **optional)
    eng_description = models.TextField(**optional)
    swe_description = models.TextField(**optional)
    shown = models.BooleanField(default=True)
    media_file = models.FileField(upload_to=videos_directory_path)
    thumbnail = models.FileField(upload_to=thumbnail_diretory_path, **optional)
    file_url = models.CharField( max_length=255, help_text="Upload file manually to /media/videos folder in nginx and paste the MEDIA URL here", **optional)
    display_to_seller = models.BooleanField(default=False)
    display_to_buyer = models.BooleanField(default=False)
    
    class Meta: 
        ordering = ['-id']

    def __str__(self): 
        return self.eng_title
    
    def save(self, *args, **kwargs): 
        if self.media_file and not self.file_url: 
            self.file_url = urljoin(BE_BASE_URL, self.media_file.url)
        super().save(*args, **kwargs)
