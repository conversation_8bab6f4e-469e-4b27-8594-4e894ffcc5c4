from rest_framework.permissions import BasePermission

class IsSuperAdminAndStaff(BasePermission):
    """
    Allows access only to superadmin and staff.
    """

    def has_permission(self, request, view):
        return bool(request.user.is_superuser or request.user.is_staff)
    

class IsSellerOrBuyerOfOrder(BasePermission):
    def has_object_permission(self, request, view, obj):
        user = request.user
        if hasattr(user, 'seller_employee_profile'):
            return obj.seller_company == user.seller_employee_profile.seller_company
        elif hasattr(user, 'buyer_employee_profile'):
            return obj.buyer_company == user.buyer_employee_profile.buyer_company
        elif hasattr(user, 'customer_account'):
            return obj.account == user.customer_account
        return False

class IsSellerOfOrder(BasePermission):
    def has_object_permission(self, request, view, obj):
        user = request.user
        if hasattr(user, 'seller_employee_profile'):
            return obj.seller_company == user.seller_employee_profile.seller_company
        return False

class IsBuyerOfOrder(BasePermission):
    def has_object_permission(self, request, view, obj):
        user = request.user
        if hasattr(user, 'buyer_employee_profile'):
            return obj.buyer_company == user.buyer_employee_profile.buyer_company
        elif hasattr(user, 'customer_account'):
            return obj.account == user.customer_account
        return False