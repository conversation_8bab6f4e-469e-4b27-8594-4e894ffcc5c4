from buyer.models import BuyerCompanySetting
from test_base.base import BaseTestCase
from test_base.factories import (
    AddressFactory, SellerCompanyFactory, BuyerSellerEmployeeAccessFactory
)

class BuyerCompanyModelTest(BaseTestCase):

    def test_buyer_company_creation(self):
        self.assertIsNotNone(self.buyer_company.company)
        self.assertIsNotNone(self.buyer_company.credit_limit)
        self.assertEqual(self.buyer_company.company.name, "Test Company")

    def test_buyer_company_str_method(self):
        expected_str = "Buyer: Test Company"
        self.assertEqual(str(self.buyer_company), expected_str)

class BuyerCompanyEmployeeModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.access = BuyerSellerEmployeeAccessFactory(buyer_employee=self.buyer_employee, seller_company=self.seller_company)

    def test_buyer_employee_creation(self):
        self.assertIsNotNone(self.buyer_employee.user)
        self.assertIsNotNone(self.buyer_employee.buyer_company)
        self.assertIsNotNone(self.buyer_employee.role)
        self.assertEqual(self.buyer_employee.user.email, "<EMAIL>")
        self.assertEqual(self.buyer_employee.buyer_company, self.buyer_company)

    def test_buyer_employee_str_method(self):
        expected_str = f"{self.buyer_employee.user} - {self.buyer_employee.buyer_company} ({self.buyer_employee.role})"
        self.assertEqual(str(self.buyer_employee), expected_str)

    def test_has_access_to_seller(self):
        self.assertTrue(self.buyer_employee.has_access_to_seller(self.seller_company))
        another_seller = SellerCompanyFactory()
        self.assertFalse(self.buyer_employee.has_access_to_seller(another_seller))

class BuyerCompanySettingModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.buyer_company_setting = BuyerCompanySetting.objects.filter(buyer_company=self.buyer_company).first()

    def test_buyer_company_setting_creation(self):
        self.assertIsNotNone(self.buyer_company_setting.buyer_company)
        self.assertIsNotNone(self.buyer_company_setting.notification_preferences)

    def test_buyer_company_setting_str_method(self):
        expected_str = f"Settings for {self.buyer_company_setting.buyer_company}"
        self.assertEqual(str(self.buyer_company_setting), expected_str)

class AddressModelTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.address = AddressFactory(
            buyer_company=self.buyer_company,
            address_type="SHIPPING"
        )

    def test_address_creation(self):
        self.assertIsNotNone(self.address.buyer_company)
        self.assertIn(self.address.address_type, ['SHIPPING', 'BILLING'])
        self.assertEqual(self.address.buyer_company, self.buyer_company)
        self.assertEqual(self.address.address_type, "SHIPPING")

    def test_address_str_method(self):
        expected_str = f"{self.address.address_type} address for {self.address.buyer_company.company.name}"
        self.assertEqual(str(self.address), expected_str)

# Add more test classes for other models or methods in the buyer app if needed