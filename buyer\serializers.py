from rest_framework import serializers
from rest_framework import serializers
from .models import BuyerCompany, BuyerCompanyEmployee, BuyerCompanySetting, Address, BuyerSellerEmployeeAccess, BuyerOrderNote
from utils.base_serializers import BaseModelSerializer, BuyerCompanyRestrictedPrimaryKeyRelatedField
from common.serializers import (
    BaseOrderListSerializer, BaseEventLogSerializer, BaseNotificationSerializer, Role,
    BasePermissionSerializer, BaseRoleSerializer, BaseCompanySerializer, NestedUserSerializer,
    UserSerializerWithoutEmail, BaseOrderItemSerializer, BaseOrderDetailSerializer
)
from rest_framework.exceptions import ValidationError as DRFValidationError
from common.mixins import CompanyRoleSerializerMixin
from common.models import User, Order, OrderItem
from django.db import transaction
from seller.models import BuyerSellerRelationship
from common.enums import ReturnStatuses
from seller.models import Product, ProductVariant, Exchange

class CurrentBuyerCompanyDefault:
    requires_context = True

    def __call__(self, serializer_field):
        request = serializer_field.context.get('request')
        if request and hasattr(request, 'user'):
            buyer_employee = BuyerCompanyEmployee.objects.filter(user=request.user).first()
            if buyer_employee:
                return buyer_employee.buyer_company
        raise serializers.ValidationError({"eng": "User is not associated with a buyer company", "swe": "Användaren är inte associerad med köparföretag"})


class AddressSerializer(BaseModelSerializer):
    buyer_company = serializers.HiddenField(default=CurrentBuyerCompanyDefault())
    class Meta:
        model = Address
        fields = '__all__'

    def validate_buyer_company(self, value):
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            buyer_employee = BuyerCompanyEmployee.objects.filter(user=request.user).first()
            if buyer_employee and value != buyer_employee.buyer_company:
                raise serializers.ValidationError({"eng": "Cannot change the buyer company", "swe": "Kan inte ändra köparföretaget"})
        return value


class BuyerPermissionSerializer(BasePermissionSerializer): 
    class Meta(BasePermissionSerializer.Meta): 
        fields = BasePermissionSerializer.Meta.fields

class BuyerRoleSerializer(CompanyRoleSerializerMixin, BaseRoleSerializer):
    class Meta(BaseRoleSerializer.Meta):
        fields = ['id', 'uuid', 'created_at', 'updated_at', 'name', 'permissions']
        read_only_fields = ['content_type', 'object_id']


class BuyerCompanyEmployeeSerializer(BaseModelSerializer):
    buyer_company = serializers.HiddenField(default=CurrentBuyerCompanyDefault())
    user = NestedUserSerializer()
    role = BuyerCompanyRestrictedPrimaryKeyRelatedField(queryset=Role.objects.all())

    custom_fields = {
        "role": BuyerRoleSerializer
    }

    class Meta:
        model = BuyerCompanyEmployee
        fields = '__all__'
        read_only_fields = ['user']

    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', None)
        if user_data:
            user = instance.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()
        return super().update(instance, validated_data)

class BuyerCompanySettingSerializer(BaseModelSerializer):
    class Meta:
        model = BuyerCompanySetting
        fields = '__all__'

class BuyerCompanyDetailSerializer(BaseModelSerializer):
    company = BaseCompanySerializer()
    settings = BuyerCompanySettingSerializer(read_only=True)
    employees = BuyerCompanyEmployeeSerializer(many=True, read_only=True)
    addresses = AddressSerializer(many=True, read_only=True, source='company.addresses')

    class Meta:
        model = BuyerCompany
        fields = '__all__'


class BuyerOrderItemSerializer(BaseOrderItemSerializer):
    class Meta(BaseOrderItemSerializer.Meta):
        fields = ['id', 'uuid', 'product', 'product_variant', 'quantity', 'description', 'vat_rate', 'price', 'discount_percentage', 'discount_amount']
        read_only_fields = ['id', 'uuid', 'description', 'vat_rate', 'price', 'discount_percentage', 'discount_amount']
        write_only_fields = ['product', 'product_variant'] # TODO: this isn't taking effect

class BuyerOrderListSerializer(BaseOrderListSerializer):
    class Meta(BaseOrderListSerializer.Meta):
        fields = BaseOrderListSerializer.Meta.fields + ['shipping_address', 'billing_address']

class BuyerOrderDetailSerializer(BaseOrderDetailSerializer):
    items = BuyerOrderItemSerializer(many=True)
    shipping_address = BuyerCompanyRestrictedPrimaryKeyRelatedField(queryset=Address.objects.all())
    billing_address = BuyerCompanyRestrictedPrimaryKeyRelatedField(queryset=Address.objects.all())

    class Meta(BaseOrderDetailSerializer.Meta):
        fields = BaseOrderDetailSerializer.Meta.fields + ['shipping_address', 'billing_address']
        read_only_fields = BaseOrderDetailSerializer.Meta.read_only_fields + ['buyer_company', 'seller_company']

    def create(self, validated_data):
        from seller.models import DiscountCode
        items_data = validated_data.pop('items')
        shipping_address = validated_data.pop('shipping_address')
        billing_address = validated_data.pop('billing_address')
        validated_data['shipping_address'] = {
            'address': shipping_address.street_address,
            'city': shipping_address.city,
            'zip_code': shipping_address.zip_code,
            'country_code': shipping_address.country_code
        }
        validated_data['billing_address'] = {
            'address': billing_address.street_address,
            'city': billing_address.city,
            'zip_code': billing_address.zip_code,
            'country_code': billing_address.country_code
        }
        discount_code = validated_data.pop('discount_code', None)
        discount_code_obj = DiscountCode.objects.filter(code=discount_code).first() # TODO: Must filter on seller_company
        validated_data['account'] = self.context['request'].user.buyer_employee_profile
        if discount_code_obj:
            validated_data['discount_code'] = discount_code_obj
        order = Order.objects.create(**validated_data)
        for item_data in items_data:
            product = item_data['product']
            product_variant = item_data.get('product_variant', None)
            quantity = item_data['quantity']
            
            
            OrderItem.objects.create(
                order=order,
                product=product,
                quantity=quantity,
                product_variant=product_variant
            )

        return order


class BuyerNotificationSerializer(BaseNotificationSerializer): 
    class Meta(BaseNotificationSerializer.Meta): 
        fields = BaseNotificationSerializer.Meta.fields


class BuyerEventLogSerializer(BaseEventLogSerializer): 
    class Meta(BaseEventLogSerializer.Meta): 
        fields = BaseEventLogSerializer.Meta.fields


class BuyerCompanySerializer(BaseModelSerializer):
    company = BaseCompanySerializer(required=False)

    class Meta:
        model = BuyerCompany
        exclude = ['created_at', 'updated_at']

    def update(self, instance, validated_data):
        if 'company' in validated_data:
            company_data = validated_data.pop('company')
            company_instance = instance.company

            # Update Company instance
            for attr, value in company_data.items():
                setattr(company_instance, attr, value)
            company_instance.save()

        # Update BuyerCompany instance
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance

class BuyerSellerEmployeeAccessSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = BuyerSellerEmployeeAccess
        fields = ['id', 'uuid', 'buyer_employee', 'seller_company', 'credit_limit', 'created_at', 'updated_at']

    def to_internal_value(self, data):
        # First, do the basic field validation
        try:
            validated_data = super().to_internal_value(data)
        except serializers.ValidationError as exc:
            raise DRFValidationError(exc.detail)

        buyer_employee = validated_data.get('buyer_employee')
        seller_company = validated_data.get('seller_company')

        # Check for uniqueness
        existing_access = BuyerSellerEmployeeAccess.objects.filter(
            buyer_employee=buyer_employee,
            seller_company=seller_company
        ).first()
        if existing_access and self.context['request'].method == 'POST':
            raise DRFValidationError({
                "eng": "This staff member already has access to this seller.",
                "swe": "Denna personal har redan tillgång till denna säljare."
            })
        elif existing_access and existing_access.id != self.instance.id:
            raise DRFValidationError({
                "eng": "This staff member already has access to this seller.",
                "swe": "Denna personal har redan tillgång till denna säljare."
            })

        user = self.context['request'].user
        if hasattr(user, 'buyer_employee_profile'):
            if buyer_employee.buyer_company != user.buyer_employee_profile.buyer_company:
                raise DRFValidationError({
                    "eng": "You can only create access for employees in your company.",
                    "swe": "Du kan bara skapa åtkomst för anställda i ditt företag."
                })
            if not seller_company.webshop_is_public and not BuyerSellerRelationship.objects.filter(
                buyer_company=buyer_employee.buyer_company,
                seller_company=seller_company
            ).exists():
                raise DRFValidationError({
                    "eng": "This company has no public webshop and has not invited you for access.",
                    "swe": "Detta företag har inte en öppen hemsida och har inte bjudit in er för tillgång."
                })

        return validated_data

    def create(self, validated_data):
        try:
            return super().create(validated_data)
        except Exception as e:
            raise DRFValidationError(str(e))

    def update(self, instance, validated_data):
        try:
            return super().update(instance, validated_data)
        except Exception as e:
            raise DRFValidationError(str(e))



class BuyerCompanyEmployeeInviteSerializer(serializers.ModelSerializer):
    user = UserSerializerWithoutEmail(required=False)
    role = BuyerCompanyRestrictedPrimaryKeyRelatedField(queryset=Role.objects.all())
    email = serializers.EmailField(write_only=True)

    class Meta:
        model = BuyerCompanyEmployee
        fields = ['user', 'role', 'email', 'is_active']
        read_only_fields = ['is_active']

    def validate_email(self, value):
        buyer_company = self.context['request'].user.buyer_employee_profile.buyer_company
        if BuyerCompanyEmployee.objects.filter(user__email=value, buyer_company=buyer_company).exists():
            raise serializers.ValidationError("An employee with this email already exists in your company.")
        return value

    def create(self, validated_data):
        with transaction.atomic():
            email = validated_data.pop('email')
            role = validated_data.pop('role')
            user_data = validated_data.pop('user', {})

            user, created = User.objects.get_or_create(email=email)
            
            if user_data:
                for key, value in user_data.items():
                    setattr(user, key, value)
                user.save()

            if created:
                user.set_unusable_password()
                user.save()

            buyer_company = self.context['request'].user.buyer_employee_profile.buyer_company
            employee = BuyerCompanyEmployee.objects.create(
                user=user,
                buyer_company=buyer_company,
                role=role,
                is_active=True
            )
            return employee
        

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['user']['email'] = instance.user.email
        representation['buyer_company'] =  instance.buyer_company.id
        return representation
    

class BuyerOrderNoteSerializer(BaseModelSerializer):
    buyer_company = serializers.HiddenField(default=CurrentBuyerCompanyDefault())
    class Meta:
        model = BuyerOrderNote
        fields = '__all__'



class BuyerExchangeSerializer(BaseModelSerializer):
    original_order_item_id = serializers.PrimaryKeyRelatedField(queryset=OrderItem.objects.all(), write_only=True)
    new_product_id = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), write_only=True)
    new_product_variant_id = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False, allow_null=True, write_only=True)

    class Meta:
        model = Exchange
        fields = ['id', 'original_order_item_id', 'new_product_id', 'new_product_variant_id', 'quantity', 'reason', 'status', 'requested_at']
        read_only_fields = ['id', 'status', 'requested_at']

    @transaction.atomic
    def create(self, validated_data):
        from seller.models import Exchange, Return, ReturnItem

        original_order_item = validated_data.pop('original_order_item_id')
        new_product = validated_data.pop('new_product_id')
        new_product_variant = validated_data.pop('new_product_variant_id', None)

        exchange = Exchange.objects.create(
            original_order=original_order_item.order,
            original_order_item=original_order_item,
            new_product=new_product,
            new_product_variant=new_product_variant,
            **validated_data
        )

        # Create an associated Return
        return_obj = Return.objects.create(
            order=exchange.original_order,
            status=ReturnStatuses.REQUESTED.value,
            reason=exchange.reason,
            exchange=exchange
        )
        ReturnItem.objects.create(
            return_request=return_obj,
            order_item=exchange.original_order_item,
            quantity=exchange.quantity
        )

        return exchange