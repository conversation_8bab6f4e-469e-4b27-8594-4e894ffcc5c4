from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import BuyerCompany, BuyerCompanyEmployee, BuyerCompanySetting, Address, BuyerSellerEmployeeAccess, BuyerOrderNote
from common.models import Notification, OrderItem
from .serializers import (
    BuyerCompanySerializer, BuyerCompanyEmployeeSerializer,
    BuyerCompanySettingSerializer, AddressSerializer,
    BuyerCompanyDetailSerializer, BuyerOrderListSerializer, BuyerOrderDetailSerializer, 
    BuyerEventLogSerializer, BuyerNotificationSerializer, 
    BuyerPermissionSerializer, BuyerRoleSerializer,
    BuyerSellerEmployeeAccessSerializer, BuyerCompanyEmployeeInviteSerializer,
    BuyerOrderNoteSerializer, BuyerExchangeSerializer
)
from common.enums import CompanyTypes, NotificationTypes
from utils.base_views import BaseModelView
from rest_framework.exceptions import ValidationError as DRFValidationError
from common.views import BaseOrderViewSet, BaseRoleViewSet, BaseEventLogViewSet, BasePermissionViewSet, BaseNotificationViewSet
from drf_yasg.utils import swagger_auto_schema
from seller.serializers import ReturnSerializer
from django.db import transaction
from seller.services import WarehouseService




class BuyerCompanyEmployeeViewSet(BaseModelView):
    queryset = BuyerCompanyEmployee.objects.all()
    serializer_class = BuyerCompanyEmployeeSerializer
    filterset_fields = ['buyer_company', 'user__email', 'is_active']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    internal_endpoint = True
    external_endpoint = False

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, uuid=None):
        employee = self.get_object()
        employee.is_active = not employee.is_active
        employee.save()
        return Response({'eng': 'Employee status updated', 'swe': 'Sstatus uppdaterad'})

    @action(detail=True, methods=['get'])
    def permissions(self, request, uuid=None):
        employee = self.get_object()
        permissions = employee.role.permissions.all() if employee.role else []
        page = self.paginate_queryset(permissions)
        if page is not None: 
            serializer = BuyerPermissionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = BuyerPermissionSerializer(permissions, many=True)
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)
    
    @swagger_auto_schema(
        request_body=BuyerCompanyEmployeeInviteSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=False, methods=['post'])
    def invite(self, request):
        buyer_company = self.get_user_company(request.user)
        current_users = buyer_company.company.get_company_type_instance().get_employees().count()
        allowed_users = 10 # TODO: how should we limit buyer companies amount of users?
        if current_users >= allowed_users:
            return Response({
                'eng': 'Maximum number of users reached. Add an add-on service to be able to invite more users.', 
                'swe': 'Maximalt antal användare nådd. Lägg till en tilläggsmodul för att kunna bjuda in fler användare.'
            }, status=status.HTTP_400_BAD_REQUEST)
        serializer = BuyerCompanyEmployeeInviteSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            employee = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        serializer.save()

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

class BuyerCompanySettingViewSet(BaseModelView):
    queryset = BuyerCompanySetting.objects.all()
    serializer_class = BuyerCompanySettingSerializer
    filterset_fields = ['buyer_company']
    internal_endpoint = True
    external_endpoint = False

    @action(detail=True, methods=['post'])
    def update_notification_preferences(self, request, uuid=None):
        setting = self.get_object()
        new_preferences = request.data.get('notification_preferences', {})
        setting.notification_preferences.update(new_preferences)
        setting.save()
        return Response({'status': 'Notification preferences updated'})
    
    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 



class AddressViewSet(BaseModelView):
    queryset = Address.objects.all()
    serializer_class = AddressSerializer
    filterset_fields = ['buyer_company', 'address_type']
    search_fields = ['street_address', 'city', 'zip_code', 'country_code']
    internal_endpoint = True
    external_endpoint = False

    @action(detail=False, methods=['get'])
    def by_company(self, request):
        buyer_company_id = request.query_params.get('buyer_company_id')
        if not buyer_company_id:
            return Response({'error': 'buyer_company_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        addresses = Address.objects.filter(buyer_company_id=buyer_company_id)
        page = self.paginate_queryset(addresses)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(addresses, many=True)
        return Response(serializer.data)


class BuyerOrderViewSet(BaseOrderViewSet):
    internal_endpoint = True

    def get_serializer_class(self):
        if self.action == 'list':
            return BuyerOrderListSerializer
        return BuyerOrderDetailSerializer

    @action(detail=False, methods=['post'])
    def place_order(self, request):
        from seller.models import SellerCompanySetting

        with transaction.atomic():
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # Set the buyer_company or account
            user = request.user
            if hasattr(user, 'customer_account'):
                serializer.validated_data['account'] = user.customer_account
            elif hasattr(user, 'buyer_employee_profile'):
                serializer.validated_data['buyer_company'] = user.buyer_employee_profile.buyer_company
            else:
                return Response({"error": "Invalid user type for placing an order"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Set the seller_company (assuming it's associated with the products being ordered)
            first_product = serializer.validated_data['items'][0]['product']
            seller_company = first_product.seller_company
            serializer.validated_data['seller_company'] = seller_company

            seller_settings = SellerCompanySetting.objects.get(seller_company=seller_company)
            
            # Get suitable warehouses based on the chosen strategy
            warehouses = WarehouseService.get_warehouses_for_order(serializer.validated_data, seller_settings)

            # Allocate order items to warehouses
            try:
                allocated_items = WarehouseService.allocate_order_items(serializer.validated_data, warehouses)
            except ValueError as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

            # Create the order
            order = serializer.save()

            # Create or update order items with allocated warehouses
            for allocation in allocated_items:
                OrderItem.objects.create(
                    order=order,
                    product=allocation['product'],
                    product_variant=allocation['product_variant'],
                    warehouse=allocation['warehouse'],
                    quantity=allocation['quantity']
                )

            WarehouseService.deduct_inventory(allocated_items)

            # Check if the order is split across warehouses
            warehouses_used = set(item['warehouse'] for item in allocated_items)
            if len(warehouses_used) > 1:
                order.is_split_order = True
                order.save()

            # Create notification for seller
            Notification.objects.create(
                recipient=order.seller_company,
                notification_type=NotificationTypes.NEW_ORDER,
                title=f"New Order {order.order_number}",
                message=f"A new order has been placed",
                related_order=order
            )
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(
        request_body=ReturnSerializer,
        responses={201: 'Created', 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def request_return(self, request, uuid=None):
        order = self.get_object()
        return_serializer = ReturnSerializer(data=request.data, context={'order': order})
        return_serializer.is_valid(raise_exception=True)
        return_request = return_serializer.save()

        # Create notification for seller
        Notification.objects.create(
            recipient=order.seller_company,
            notification_type=NotificationTypes.RETURN_REQUESTED,
            title=f"Return Requested for Order {order.order_number}",
            message=f"A return has been requested",
            related_order=order,
            related_return=return_request
        )

        return Response(return_serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(
        request_body=BuyerExchangeSerializer,
        responses={201: BuyerExchangeSerializer, 400: 'Bad Request', 500: 'Internal Server Error'}
    )
    @action(detail=True, methods=['post'])
    def request_exchange(self, request, uuid=None):
        order = self.get_object()
        exchange_serializer = BuyerExchangeSerializer(data=request.data, context={'order': order})
        exchange_serializer.is_valid(raise_exception=True)
        exchange_request = exchange_serializer.save()

        # Create notification for seller
        Notification.objects.create(
            recipient=order.seller_company,
            notification_type=NotificationTypes.EXCHANGE_REQUESTED,
            title=f"Exchange Requested for Order {order.order_number}",
            message=f"An exchange has been requested for order {order.order_number}",
            related_order=order,
            related_exchange=exchange_request
        )

        return Response(BuyerExchangeSerializer(exchange_request).data, status=status.HTTP_201_CREATED)
    
    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 
    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        pass 
    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, *args, **kwargs):
        pass 




class BuyerPermissionViewSet(BasePermissionViewSet):
    serializer_class = BuyerPermissionSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(company_types__icontains=CompanyTypes.BUYER.name)
        return queryset

class BuyerRoleViewSet(BaseRoleViewSet):
    serializer_class = BuyerRoleSerializer
    internal_endpoint = True
    external_endpoint = False

    def get_company_model(self):
        return BuyerCompany

    def get_company_profile_attr(self):
        return 'buyer_employee_profile'

class BuyerEventLogViewSet(BaseEventLogViewSet):
    serializer_class = BuyerEventLogSerializer
    internal_endpoint = True

class BuyerNotificationViewSet(BaseNotificationViewSet):
    serializer_class = BuyerNotificationSerializer
    internal_endpoint = True




class BuyerCompanyViewSet(BaseModelView):
    queryset = BuyerCompany.objects.all()
    serializer_class = BuyerCompanySerializer
    filterset_fields = ['company__name', 'company__is_active']
    search_fields = ['company__name', 'company__organisation_number']
    internal_endpoint = True

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return BuyerCompanyDetailSerializer
        return self.serializer_class

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user
        user_company = self.get_user_company(user)

        if user_company:
            # Filter based on the user's company or customer account
            company_filter = self.get_company_filter(user_company)
            if company_filter:
                queryset = queryset.filter(**company_filter)

        return queryset
    
    @action(detail=True, methods=['get'])
    def employees(self, request, uuid=None):
        buyer_company = self.get_object()
        employees = BuyerCompanyEmployee.objects.filter(buyer_company=buyer_company)
        page = self.paginate_queryset(employees)
        if page is not None:
            serializer = BuyerCompanyEmployeeSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = BuyerCompanyEmployeeSerializer(employees, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get', 'post'])
    def company_settings(self, request, uuid=None):
        buyer_company = self.get_object()
        settings, created = BuyerCompanySetting.objects.get_or_create(buyer_company=buyer_company)

        if request.method == 'POST':
            serializer = BuyerCompanySettingSerializer(settings, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        serializer = BuyerCompanySettingSerializer(settings)
        return Response(serializer.data)

    
    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        pass 

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        pass 
    
class BuyerSellerEmployeeAccessViewSet(BaseModelView):
    queryset = BuyerSellerEmployeeAccess.objects.all()
    serializer_class = BuyerSellerEmployeeAccessSerializer
    filterset_fields = ['buyer_employee', 'seller_company']
    search_fields = ['buyer_employee__user__email', 'seller_company__company__name']
    internal_endpoint = True
    

    def get_object(self):
        return self.get_queryset().get(uuid=self.kwargs['uuid'])

    def perform_create(self, serializer):
        from seller.models import BuyerSellerRelationship
        user = self.request.user
        try:
            if hasattr(user, 'buyer_employee_profile'):
                serializer.save()
            else:
                super().perform_create(serializer)
        except Exception as ex: 
            print(ex)

    def get_queryset(self): 
        queryset = BuyerSellerEmployeeAccess.objects.all()
        user = self.request.user
        if hasattr(user, 'buyer_employee_profile'):
            return queryset.filter(buyer_employee__buyer_company=user.buyer_employee_profile.buyer_company)
        elif hasattr(user, 'seller_employee_profile'):
            return queryset.filter(seller_company=user.seller_employee_profile.seller_company)
        return queryset.none()
    

class BuyerOrderNoteViewSet(BaseModelView):
    queryset = BuyerOrderNote.objects.all()
    serializer_class = BuyerOrderNoteSerializer
    filterset_fields = ['order']
    internal_endpoint = True