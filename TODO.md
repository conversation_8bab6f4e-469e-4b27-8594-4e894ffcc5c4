make sure credit limits and permissions are implemented as expected 


payments through stripe/invoice

send emails upon signup etc

remove ability to do anything if subscription/company is is_active = False (after cancellation)

integrations 
    - warehouse 
    - accounting 
    - 3PL

- keep sub active until period end date has passed (after cancellation)
- same for downgrade
- charge customer for upgrading


plan FE with claude

VAT RATE LOGIC for OSS etc
we create ALL country objects and VAT category objects
they create VAT categories and VAT rates and connects products/product variants to VAT categories. It's OUR job to apply the correct VAT rate depending on the country. 


reset warehouse invetory if an order is cancelled 


fix test_product_detail error regarding 
This error is occurring because you're trying to serialize a float value that's out of the range that JSON can represent. This typically happens with very large numbers, infinity, or NaN (Not a Number) values.