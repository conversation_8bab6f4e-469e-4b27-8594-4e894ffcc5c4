from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    BuyerCompanyViewSet, BuyerCompanyEmployeeViewSet,
    BuyerCompanySettingViewSet, AddressViewSet, BuyerOrderViewSet,
    BuyerNotificationViewSet, BuyerEventLogViewSet, BuyerRoleViewSet, 
    BuyerPermissionViewSet, BuyerSellerEmployeeAccessViewSet, 
    BuyerOrderNoteViewSet
)

router = DefaultRouter()
router.register(r'companies', BuyerCompanyViewSet)
router.register(r'employees', BuyerCompanyEmployeeViewSet)
# router.register(r'settings', BuyerCompanySettingViewSet) # this can be accessed via /buyer/companies/{uuid}/company_settings
router.register(r'addresses', AddressViewSet)
router.register(r'orders', BuyerOrderViewSet, basename='buyerorder')
router.register(r'permissions', BuyerPermissionViewSet, basename='buyerpermission')
router.register(r'roles', BuyerRoleViewSet, basename='buyerrole')
router.register(r'event-logs', BuyerEventLogViewSet, basename='buyereventlog')
router.register(r'notifications', BuyerNotificationViewSet, basename='buyernotification')
router.register(r'seller-access', BuyerSellerEmployeeAccessViewSet)
router.register(r'order-notes', BuyerOrderNoteViewSet)

urlpatterns = [
    path('', include(router.urls)),
]