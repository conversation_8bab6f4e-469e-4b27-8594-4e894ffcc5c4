from django.urls import reverse
from rest_framework import status
from decimal import Decimal
from common.models import Company
from seller.models import (
    ProductCategory, Product, Warehouse, WarehouseInventory
)
from test_base.base import BaseAPITestCase
from common.enums import DiscountTypes
from test_base.factories import (
    RoleFactory, CompanyFactory, SellerCompanyFactory, SellerCompanyEmployeeFactory,
    BuyerCompanyFactory, BuyerSellerRelationshipFactory, EventLogFactory,
    WarehouseFactory, WarehouseInventoryFactory, ProductFactory, ProductVariantFactory, 
    OrderItemFactory, OrderFactory, BuyerCompanyEmployeeFactory, ProductCategoryFactory,
    CountryFactory
)

class SellerAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(
            name="Test Seller Company",
            organisation_number="12345",
            street_address="Test Street",
            zip_code="12345",
            city="Test City",
            contact_phone_number="1234567890",
            contact_email="<EMAIL>"
        )
        self.seller_company = SellerCompanyFactory(
            company=self.company,
            webshop_is_public=True,
            domain="testsellercompany.com"
        )
        self.seller_employee = SellerCompanyEmployeeFactory(
            seller_company=self.seller_company,
            user=self.user,
            role=RoleFactory(name="Test Role", company=self.seller_company)
        )
        self.category = ProductCategoryFactory(
            seller_company=self.seller_company,
            name="Test Category"
        )
        self.product = ProductFactory(
            seller_company=self.seller_company,
            name="Test Product",
            description="Test Description",
            base_price=Decimal("100.00"),
            category=self.category,
            slug="test-product"
        )
        self.buyer_company = BuyerCompanyFactory(
            company=CompanyFactory(name="Test Buyer Company"),
            credit_limit=Decimal("1000.00")
        )
        self.buyer_seller_relationship = BuyerSellerRelationshipFactory(
            buyer_company=self.buyer_company,
            seller_company=self.seller_company,
            is_active=True
        )

    def test_seller_company_list(self):
        url = reverse('sellercompany-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_seller_company_detail(self):
        url = reverse('sellercompany-detail', kwargs={'uuid': self.seller_company.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['company']['name'], "Test Seller Company")

    def test_seller_company_employee_list(self):
        url = reverse('sellercompanyemployee-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_category_list(self):
        url = reverse('productcategory-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_product_list(self):
        url = reverse('product-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_product_detail(self):
        url = reverse('product-detail', kwargs={'uuid': self.product.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['name'], "Test Product")

    def test_product_variants(self):
        url = reverse('product-variants', kwargs={'uuid': self.product.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_discount_list(self):
        url = reverse('discount-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_discount_create(self):
        url = reverse('discount-list')
        data = {
            'seller_company': self.seller_company.pk,
            'name': 'Test Discount',
            'discount_type': DiscountTypes.PERCENTAGE.name,
            'value': 10,
            'start_date': '2023-01-01T00:00:00Z',
            'end_date': '2023-12-31T23:59:59Z'
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Discount')

    def test_price_list_list(self):
        url = reverse('pricelist-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreaterEqual(len(response.data['results']), 0) # Greater because one is created by default when company is created

    def test_price_list_create(self):
        url = reverse('pricelist-list')
        data = {
            'seller_company': self.seller_company.pk,
            'name': 'Test Price List',
            'is_default': True
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Price List')

    def test_warehouse_list(self):
        url = reverse('warehouse-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_warehouse_create(self):
        url = reverse('warehouse-list')
        data = {
            'seller_company': self.seller_company.pk,
            'name': 'Test Warehouse',
            'address': 'Test Warehouse Address',
            'manager': self.seller_employee.pk,
            'city': 'Test City',
            'country': CountryFactory().pk,
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Warehouse')

    def test_order_category_list(self):
        url = reverse('ordercategory-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_order_category_create(self):
        url = reverse('ordercategory-list')
        data = {
            'seller_company': self.seller_company.pk,
            'name': 'Test Order Category',
            'color': '#FF0000'
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Order Category')

    def test_return_list(self):
        url = reverse('return-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_exchange_list(self):
        url = reverse('exchange-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    # def test_packing_slip_list(self):
    #     url = reverse('packingslip-list')
    #     response = self.api_get(url)
    #     self.assertSuccessResponse(response)
    #     self.assertEqual(len(response.data['results']), 0)

    def test_document_category_list(self):
        url = reverse('documentcategory-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_document_list(self):
        url = reverse('document-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_seller_order_list(self):
        url = reverse('sellerorder-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_seller_permission_list(self):
        url = reverse('sellerpermission-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_seller_role_list(self):
        url = reverse('sellerrole-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreaterEqual(len(response.data['results']), 1)

    def test_seller_event_log_list(self):
        EventLogFactory(user=self.user, event_type='TEST_EVENT')
        url = reverse('sellereventlog-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreaterEqual(len(response.data['results']), 1)

    def test_seller_notification_list(self):
        url = reverse('sellernotification-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_buyer_seller_relationship_list(self):
        url = reverse('buyersellerrelationship-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_buyer_seller_relationship_detail(self):
        url = reverse('buyersellerrelationship-detail', kwargs={'uuid': self.buyer_seller_relationship.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['is_active'], True)


class SellerOrderViewSetTestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.seller_company = SellerCompanyFactory()
        self.warehouse = WarehouseFactory(seller_company=self.seller_company)
        self.product = ProductFactory(seller_company=self.seller_company)
        self.variant = ProductVariantFactory(product=self.product)
        self.buyer_company = BuyerCompanyFactory()
        self.buyer_employee = BuyerCompanyEmployeeFactory(buyer_company=self.buyer_company, user=self.user)
        WarehouseInventoryFactory(warehouse=self.warehouse, product_variant=self.variant, quantity=10)

    def test_ship_items(self):
        order = OrderFactory(seller_company=self.seller_company, buyer_company=self.buyer_company)
        order_item = OrderItemFactory(order=order, product=self.product, product_variant=self.variant, quantity=2)
        
        url = reverse('sellerorder-ship-items', kwargs={'uuid': order.uuid})
        data = {
            'items': [
                {
                    'id': order_item.id,
                    'quantity': 1,
                    'warehouse_id': self.warehouse.id
                }
            ]
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response)
        
        order_item.refresh_from_db()
        self.assertEqual(order_item.shipped_quantity, 1)
        self.assertEqual(order_item.warehouse, self.warehouse)