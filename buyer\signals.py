from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import BuyerCompany, BuyerCompanySetting
from common.models import Role, Permission
from common.enums import CompanyTypes

from django.contrib.contenttypes.models import ContentType

@receiver(post_save, sender=BuyerCompany)
def buyer_company_setup(sender, instance, created, **kwargs):
    if created:
        BuyerCompanySetting.objects.create(buyer_company=instance)

        # Create an "Administrator" role with all permissions related to company_type BUYER
        admin_role = Role.objects.create(
            name="Administratör",
            content_type=ContentType.objects.get_for_model(instance),
            object_id=instance.id
        )
        buyer_permissions = Permission.objects.filter(company_types__icontains=CompanyTypes.BUYER.name)
        admin_role.permissions.set(buyer_permissions)
