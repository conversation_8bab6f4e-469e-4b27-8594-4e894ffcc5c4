from rest_framework import serializers
import importlib
from django.db.models import Manager
from common.models import Company

class BaseModelSerializer(serializers.ModelSerializer):
    class Meta:
        abstract = True

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context.get('request')
        if request and request.query_params.get('fields'):
            fields = request.query_params.get('fields').split(',')
            allowed = set(fields)
            existing = set(self.fields.keys())
            for field_name in existing - allowed:
                self.fields.pop(field_name)
    
    def to_representation(self, instance):
        """
        Overwrite the primary key value with a full serialization of some FK instances in the to_representation method. 
        This method is only used when serializing instances to JSON, not when validating or deserializing JSON to instances, 
        so it won't affect the payload you need to send when creating or updating instances.
        It will be applied to the fields in the custom_fields dictionary that are set in each serializer inheriting from this serializer.
        """
        representation = super().to_representation(instance)
        request = self.context.get('request')
        if request and request.query_params.get('omit'):
            omit_fields = request.query_params.get('omit').split(',')
            for field in omit_fields:
                representation.pop(field, None)
        
        # Check if the child serializer has 'custom_fields' attribute
        if hasattr(self, 'custom_fields'):
            for field, serializer in self.custom_fields.items():
                value = getattr(instance, field)

                if isinstance(serializer, str):  # If the serializer is a string (module path)
                    module_name, class_name = serializer.rsplit(".", 1)
                    SerializerClass = getattr(importlib.import_module(module_name), class_name)
                else:  # If the serializer is a class
                    SerializerClass = serializer

                if value is not None:
                    if isinstance(value, Manager):  # if 'value' is a related objects manager
                        representation[field] = SerializerClass(value.all(), many=True).data
                    else:  # if 'value' is a single related instance
                        representation[field] = SerializerClass(value).data
                else:
                    representation[field] = None

        return representation
    
class BuyerCompanyRestrictedPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        request = self.context.get('request', None)
        queryset = super().get_queryset()

        if not request or not queryset:
            return queryset.none()  # Return an empty queryset

        buyer_company = request.user.buyer_employee_profile.buyer_company

        # Check if the model has a field for buyer_company or company
        if hasattr(queryset.model, 'buyer_company'):
            return queryset.filter(buyer_company=buyer_company)
        elif queryset.model == Company:
            # Assuming BuyerCompany has a ForeignKey to Company
            return queryset.filter(id=buyer_company.company.id)
        elif hasattr(queryset.model, 'buyer_employees'):
            # Assuming Role is related to BuyerCompany through a ManyToMany or reverse ForeignKey
            return queryset.filter(buyer_employees__buyer_company=buyer_company).distinct()
        elif hasattr(queryset.model, 'company'):
            return queryset.filter(id=buyer_company.company.id)
        else:
            return queryset.none()   # Return an empty queryset if neither field is present
    
class SellerCompanyRestrictedPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        request = self.context.get('request', None)
        queryset = super().get_queryset()

        if not request or not queryset:
            return queryset.none()  # Return an empty queryset
        
        seller_company = request.user.seller_employee_profile.seller_company

        # Handle self-referential fields
        if queryset.model == self.parent.Meta.model:
            return queryset.filter(seller_company=seller_company)

        if hasattr(queryset.model, 'seller_company'):
            return queryset.filter(seller_company=seller_company)
        elif queryset.model == Company:
            # Assuming BuyerCompany has a ForeignKey to Company
            return queryset.filter(id=seller_company.company.id)
        elif hasattr(queryset.model, 'seller_employees'):
            # Assuming Role is related to BuyerCompany through a ManyToMany or reverse ForeignKey
            return queryset.filter(seller_employees__seller_company=seller_company).distinct()
        elif hasattr(queryset.model, 'company'):
            return queryset.filter(id=seller_company.company.id)
        elif hasattr(queryset.model, 'original_order'): 
            return queryset.filter(original_order__seller_company=seller_company)
        elif hasattr(queryset.model, 'order'):
            return queryset.filter(order__seller_company=seller_company)
        elif hasattr(queryset.model, 'product'):
            return queryset.filter(product__seller_company=seller_company)
        else:
            return queryset.none() 

