from django.contrib import admin
from .models import (
    SellerCompany, SellerCompanyEmployee, SellerCompanySetting, 
    BuyerSellerRelationship, Product, ProductPrice, ProductVariant, 
    ProductImage, PriceList, VATRate, Discount, DiscountAssignment, DiscountCode,
    Exchange, Return, ReturnItem, Document, DocumentCategory,
    ProductCategory, VATCategory, ProductVisibilityRule
)


admin.site.register(ProductVisibilityRule)
admin.site.register(SellerCompany)
admin.site.register(SellerCompanyEmployee)
admin.site.register(SellerCompanySetting)
admin.site.register(BuyerSellerRelationship)
admin.site.register(Product)
admin.site.register(ProductPrice)
admin.site.register(ProductVariant)
admin.site.register(ProductImage)
admin.site.register(PriceList)
admin.site.register(VATRate)
admin.site.register(Discount)
admin.site.register(DiscountAssignment)
admin.site.register(DiscountCode)
admin.site.register(Exchange)
admin.site.register(Return)
admin.site.register(ReturnItem)
admin.site.register(Document)
admin.site.register(DocumentCategory)
admin.site.register(ProductCategory)
admin.site.register(VATCategory)