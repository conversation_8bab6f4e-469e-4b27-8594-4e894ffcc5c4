from django.urls import reverse
from rest_framework import status
from decimal import Decimal
from common.models import User, Order
from individual_customer.models import CustomerAccount
from seller.models import SellerCompany
from test_base.base import BaseAPITestCase
from test_base.factories import UserFactory, CustomerAccountFactory, SellerCompanyFactory, OrderFactory, CompanyFactory, EventLogFactory

class IndividualCustomerAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user('<EMAIL>', 'testpassword')
        self.client.force_authenticate(user=self.user)
        self.customer_account = CustomerAccountFactory(
            user=self.user
        )

        # New user setup
        self.other_user = self.create_user('<EMAIL>', 'otherpassword')
        self.other_customer_account = CustomerAccountFactory(user=self.other_user)

        self.company = CompanyFactory()
        self.seller_company = SellerCompanyFactory(
            company=self.company
        )
        self.order = OrderFactory(
            seller_company=self.seller_company,
            account=self.customer_account,
            order_number='TEST001',
            total_amount_to_pay=Decimal('100.00'),
            total_vat_amount=Decimal('20.00'),
            total_discount_amount=Decimal('0.00'),
            status='PENDING'
        )

        # Create an order for the other user
        self.other_order = OrderFactory(
            seller_company=self.seller_company,
            account=self.other_customer_account,
            order_number='TEST002',
            total_amount_to_pay=Decimal('150.00'),
            total_vat_amount=Decimal('30.00'),
            total_discount_amount=Decimal('0.00'),
            status='PROCESSING'
        )

    def test_customer_account_list(self):
        url = reverse('customeraccount-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['user'], self.user.pk)

    def test_customer_account_detail(self):
        url = reverse('customeraccount-detail', kwargs={'uuid': self.customer_account.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['user'], self.user.pk)

    def test_customer_account_create(self):
        url = reverse('customeraccount-list')
        new_user = User.objects.create_user(
            email='<EMAIL>',
            password='newpassword',
            first_name='New',
            last_name='Customer'
        )
        data = {
            'user': new_user.pk,
        }
        response = self.api_post(url, data)
        self.assertSuccessResponse(response, status.HTTP_201_CREATED)
        self.assertEqual(CustomerAccount.objects.count(), 3)

    def test_customer_order_list(self):
        url = reverse('customerorder-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['order_number'], 'TEST001')

    def test_customer_order_detail(self):
        url = reverse('customerorder-detail', kwargs={'uuid': self.order.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(response.data['order_number'], 'TEST001')

    def test_customer_order_detail_other_user(self):
        # Attempt to access the other user's order
        url = reverse('customerorder-detail', kwargs={'uuid': self.other_order.uuid})
        response = self.api_get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_customer_notification_list(self):
        url = reverse('customernotification-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_customer_event_log_list(self):
        EventLogFactory(user=self.user, event_type="TEST_EVENT")
        url = reverse('customereventlog-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertGreater(len(response.data['results']), 1)

    def test_customer_order_expected_delivery_date(self):
        url = reverse('customerorder-detail', kwargs={'uuid': self.order.uuid})
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertIn('expected_delivery_date', response.data)

    def test_customer_account_search(self):
        url = reverse('customeraccount-list')
        response = self.api_get(url, {'search': '<EMAIL>'})
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)

    def test_customer_order_filter(self):
        url = reverse('customerorder-list')
        response = self.api_get(url, {'status': 'PENDING'})
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['order_number'], 'TEST001')

        # Ensure the other user's order is not returned
        response = self.api_get(url, {'status': 'PROCESSING'})
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 0)

    def test_customer_notification_mark_as_read(self):
        # First, create a notification
        from common.models import Notification
        notification = Notification.objects.create(
            recipient=self.customer_account,
            notification_type='ORDER_STATUS',
            title='Order Status Update',
            message='Your order status has been updated.'
        )
        
        url = reverse('customernotification-mark-as-read', kwargs={'uuid': notification.uuid})
        response = self.api_post(url)
        self.assertSuccessResponse(response)
        
        notification.refresh_from_db()
        self.assertTrue(notification.is_read)

    def test_other_user_orders_not_visible(self):
        # Switch authentication to the other user
        self.client.force_authenticate(user=self.other_user)

        url = reverse('customerorder-list')
        response = self.api_get(url)
        self.assertSuccessResponse(response)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['order_number'], 'TEST002')

        # Attempt to access the original user's order
        url = reverse('customerorder-detail', kwargs={'uuid': self.order.uuid})
        response = self.api_get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)