from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    FAQCategoryViewSet, 
    FAQViewSet, 
    BlogCategoryViewSet, 
    BlogViewSet, 
    LandingPageFAQViewSet, 
    GuideViewSet, 
    LandingPageContactFormViewSet, 
    TestimonialViewSet, 
    TermsOfPurchaseView,
    BlogCommentsViewSet,
    HelpTextListView, 
    InformationTextListView,
    VideoGuideViewSet
)

router = DefaultRouter()
router.register(r'faqs', FAQViewSet, basename="faq")
router.register(r'faq-categories', FAQCategoryViewSet, basename="faq-categories")
router.register(r'guides', GuideViewSet, basename="guides")
router.register(r'help-texts', HelpTextListView, basename='help-texts')
router.register(r'information-texts', InformationTextListView, basename='information-texts')
router.register(r'video-guides', VideoGuideViewSet, basename='video-guides')

landing_page_router = DefaultRouter()
landing_page_router.register(r'faqs', LandingPageFAQViewSet, basename="landing-pagefaq")
landing_page_router.register(r'blogs', BlogViewSet, basename="blog")
landing_page_router.register(r'blog-categories', BlogCategoryViewSet, basename="blog-categories")
landing_page_router.register(r'testimonials', TestimonialViewSet, basename="testimonials")
landing_page_router.register(r'blog-comments', BlogCommentsViewSet, basename="blog-comments")


urlpatterns = [
    path('resources/', include(router.urls)),
    path('landing-page/', include(landing_page_router.urls)),
    path('landing-page/contact/', LandingPageContactFormViewSet.as_view({'post': 'create'}), name="landing-pagecontact"),
    path('landing-page/terms-of-purchase/', TermsOfPurchaseView.as_view(), name="landing-pagetop"),
]