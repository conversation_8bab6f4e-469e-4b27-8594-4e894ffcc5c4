from django.db import models
from common.models import BaseModel
from datetime import date
from dateutil.relativedelta import relativedelta
from common.models import Company
from model_utils import FieldTracker
from common.enums import PaymentMethods, PaymentPlans, SubscriptionAddOns, PaymentTransactionStatuses, PaymentTransactionTypes
from utils.constants import DEFAULT_FE_PAGES_INCLUDED

optional = {
    "null": True,
    "blank": True
}

def get_default_fe_pages(self):
    return DEFAULT_FE_PAGES_INCLUDED

def payments_directory_path(instance, filename):
    return f'subscriptions/payments/{instance.uuid}/{filename}'

class Price(BaseModel):
    description = models.CharField(max_length=255, **optional, help_text="This is only for internal identifying, not used in invoices")
    quarterly_price = models.FloatField(default=0, help_text="This is the price that will be applied in Fortnox contracts/invoices")
    yearly_price = models.FloatField(default=0, help_text="This is the price that will be applied in Fortnox contracts/invoices")
    # stripe product IDs
    stripe_product_id = models.CharField(max_length=255, **optional)
    stripe_price_id_quarterly = models.CharField(max_length=255, **optional)
    stripe_price_id_yearly = models.CharField(max_length=255, **optional)
    # accounting product IDs
    accounting_product_id = models.CharField(max_length=255, **optional, help_text="The article number in Fortnox to use in contracts/invoices")
    # stripe payment links 
    quarterly_stripe_payment_link = models.CharField(max_length=255, **optional)
    yearly_stripe_payment_link = models.CharField(max_length=255, **optional)
    
    def __str__(self): 
        return str(self.id) + " - " + str(self.description) 
    
    class Meta: 
        ordering = ['-id']

class Plan(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    features = models.JSONField()  # JSON structure detailing what the plan includes
    price = models.ForeignKey(Price, models.PROTECT, **optional, related_name="base_plans")
    included_user_accounts = models.PositiveIntegerField(default=1)
    included_products = models.PositiveIntegerField()
    included_customers = models.PositiveIntegerField()
    modules_included = models.JSONField(default=get_default_fe_pages)  # JSON structure detailing what modules are included in the plan
    level = models.IntegerField(default=1)

    def __str__(self):
        return self.name

    def get_price(self, payment_plan):
        return self.price.yearly_price if payment_plan == PaymentPlans.YEARLY.name else self.price.quarterly_price

class Addon(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.ForeignKey(Price, models.PROTECT, **optional, related_name="addons")
    addon_type = models.CharField(max_length=50, choices=SubscriptionAddOns.choices())

    def __str__(self):
        return f"{self.name} ({self.get_addon_type_display()})"

class Subscription(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='subscriptions')
    plan = models.ForeignKey(Plan, on_delete=models.PROTECT)
    payment_plan = models.CharField(max_length=10, choices=PaymentPlans.choices())
    payment_method = models.CharField(max_length=10, choices=PaymentMethods.choices())
    start_date = models.DateField()
    end_date = models.DateField()
    current_period_start_date = models.DateField(**optional)
    current_period_end_date = models.DateField(**optional)
    cost = models.FloatField(default=0)
    is_active = models.BooleanField(default=True)
    addons = models.ManyToManyField(Addon, through='SubscriptionAddon')
    
    customer_number = models.CharField(max_length=100, **optional, help_text="Customer number for the subscribing company")
    stripe_customer_id = models.CharField(max_length=1000, **optional, help_text="Stripe customer ID for payment processing")
    stripe_subscription_id = models.CharField(max_length=255, **optional)
    accounting_contract_id = models.CharField(max_length=255, **optional)
    billing_email = models.EmailField(**optional, help_text="Email address for billing purposes")
    stripe_payment_methods = models.JSONField(**optional, help_text="Stored Stripe payment methods")
    remind_after_x_days = models.IntegerField(default=3, help_text="Number of days after an invoice's due date to send reminders")
    send_payment_reminders = models.BooleanField(default=True, help_text="If True, send emails about unpaid invoices")
    inactivate_account_x_days_after_reminder = models.IntegerField(default=5, help_text="Number of days after a reminder to inactivate the account (0 if never)")
    inactivation_reason = models.TextField(**optional, help_text="Reason for account inactivation")


    tracker = FieldTracker()

    def __str__(self):
        return f"{self.company.name} - {self.plan.name} ({self.get_payment_plan_display()})"

    def save(self, *args, **kwargs):
        changed_fields = set(self.tracker.changed().keys())
        is_new = self.pk is None
        if not self.end_date:
            self.end_date = self.calculate_end_date()
        
        if is_new:
            super().save(*args, **kwargs)
            self.cost = self.calculate_cost()
            super().save(update_fields=['cost'])
        else:
            subscription_addon_from_today = SubscriptionAddon.objects.filter(subscription=self, created_at__date__gte=date.today())
            cost_affecting_fields = ['plan', 'payment_plan']
            if any(field in changed_fields for field in cost_affecting_fields) or subscription_addon_from_today:
                self.cost = self.calculate_cost()
            super().save(*args, **kwargs)

    def calculate_end_date(self):
        if self.payment_plan == PaymentPlans.YEARLY.name:
            return self.start_date + relativedelta(years=1)
        return self.start_date + relativedelta(months=3)
    
    def calculate_cost(self):
        base_cost = self.plan.price.yearly_price if self.payment_plan == PaymentPlans.YEARLY.name else self.plan.price.quarterly_price
        
        # Calculate the cost of addons
        addon_cost = sum(
            subscription_addon.quantity * (
                subscription_addon.addon.price.yearly_price 
                if self.payment_plan == PaymentPlans.YEARLY.name 
                else subscription_addon.addon.price.quarterly_price
            )
            for subscription_addon in self.subscriptionaddon_set.all()
        )
        
        total_cost = base_cost +  addon_cost
        return total_cost

    def cancel(self, reason):
        self.is_active = False
        self.save()
        Unsubscription.objects.create(subscription=self, reason=reason)
        self.company.is_active = False
        self.company.save()

    def change_plan(self, new_plan):
        old_plan = self.plan
        old_cost = self.cost
        self.plan = new_plan
        new_cost = self.calculate_cost()
        
        days_left = (self.end_date - date.today()).days
        total_days = (self.end_date - self.start_date).days
        
        if new_cost > old_cost:  # Upgrade
            price_difference = new_cost - old_cost
            prorated_charge = (price_difference / total_days) * days_left
            self.charge_extra(prorated_charge)
        
        self.save()
        return old_plan, new_plan

    def change_payment_method(self, new_payment_method):
        old_payment_method = self.payment_method
        self.payment_method = new_payment_method
        self.save()
        return old_payment_method, new_payment_method

    def charge_extra(self, amount):
        # TODO: implement this accurately
        PaymentTransaction.objects.create(
            subscription=self,
            amount=amount,
            payment_date=date.today(),
            status=PaymentTransactionStatuses.PENDING.name,
            transaction_type=PaymentTransactionTypes.UPGRADE.name,
            due_date=date.today() + relativedelta(days=7),  # Set due date to 7 days from now
            amount_due=amount
        )

class SubscriptionAddon(BaseModel):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE)
    addon = models.ForeignKey(Addon, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)

    def __str__(self):
        return f"{self.subscription.company.name} - {self.addon.name} (x{self.quantity})"

class PaymentTransaction(BaseModel):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='transactions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DateField()
    status = models.CharField(max_length=20, choices=PaymentTransactionStatuses.choices())
    transaction_type = models.CharField(max_length=20, choices=PaymentTransactionTypes.choices())
    due_date = models.DateField(**optional,  help_text="Payment Due Date")
    amount_due = models.FloatField(default=0.0, help_text="Balance on payment")
    final_payment_date = models.DateField(**optional)
    invoice_number = models.CharField(**optional, max_length=200, help_text="If payment method is invoice, this will be the invoice number")
    payment_file = models.FileField(upload_to=payments_directory_path, blank=True, help_text="Receipt/invoice file")
    stripe_id = models.CharField(max_length=255, **optional, help_text="Stripe's unique identifier for this payment.")

    def __str__(self):
        return f"{self.subscription.company.name} - {self.amount} - {self.get_status_display()}"
    

class Unsubscription(BaseModel):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='unsubscriptions')
    reason = models.TextField()
    unsubscribed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.subscription.company.name} - {self.unsubscribed_at}"


