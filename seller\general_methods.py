from django.db.models import Count
from seller.models import Return

def analyze_returns(seller_company=None):
    returns = Return.objects.all()
    if seller_company:
        returns = returns.filter(order__seller_company=seller_company)
    
    return_analysis = returns.values('reason') \
                             .annotate(count=Count('id')) \
                             .order_by('-count')
    return return_analysis