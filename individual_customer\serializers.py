from utils.base_serializers import BaseModelSerializer
from .models import CustomerAccount
from common.serializers import BaseOrderListSerializer, BaseOrderDetailSerializer, BaseNotificationSerializer, BaseEventLogSerializer

class CustomerAccountSerializer(BaseModelSerializer):
    class Meta:
        model = CustomerAccount
        fields = '__all__'

class CustomerOrderListSerializer(BaseOrderListSerializer):
    pass

class CustomerOrderDetailSerializer(BaseOrderDetailSerializer):
    pass



class CustomerNotificationSerializer(BaseNotificationSerializer): 
    class Meta(BaseNotificationSerializer.Meta): 
        fields = BaseNotificationSerializer.Meta.fields

class CustomerEventLogSerializer(BaseEventLogSerializer): 
    class Meta(BaseEventLogSerializer.Meta): 
        fields = BaseEventLogSerializer.Meta.fields