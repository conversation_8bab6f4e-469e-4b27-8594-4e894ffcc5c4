from seller.models import SellerCompanyEmployee
from buyer.models import BuyerCompanyEmployee
from common.enums import CompanyTypes
from django.contrib.contenttypes.models import ContentType
from common.models import Notification

import pytz
from django.utils import timezone
from datetime import datetime, timedelta

TIME_ZONE = pytz.timezone('Europe/Stockholm')

# Function to make datetime objects time zone aware
def make_aware_datetime(date):
    naive_datetime = datetime.combine(date, datetime.min.time())
    return timezone.make_aware(naive_datetime, timezone=TIME_ZONE)



def has_permission(employee, codename):
    if isinstance(employee, BuyerCompanyEmployee):
        return employee.role.permissions.filter(codename=codename, company_types__icontains=CompanyTypes.BUYER.name).exists()
    elif isinstance(employee, SellerCompanyEmployee):
        return employee.role.permissions.filter(codename=codename, company_types__icontains=CompanyTypes.SELLER.name).exists()
    return False



def get_notification(obj):
    content_type = ContentType.objects.get_for_model(obj)
    return Notification.objects.filter(
        content_type=content_type,
        object_id=obj.id
    ).order_by('-created_at')

def create_notification(recipient, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    content_type = ContentType.objects.get_for_model(recipient)
    Notification.objects.create(
        content_type=content_type,
        object_id=recipient.id,
        notification_type=notification_type,
        title=title,
        message=message,
        related_order=related_order,
        related_return=related_return,
        related_exchange=related_exchange
    )

def get_notifications_for_buyer_employee(buyer_employee):
    return get_notification(buyer_employee)

def get_notifications_for_seller_employee(seller_employee):
    return get_notification(seller_employee)

def get_notification_for_customer(customer):
    return get_notification(customer)

def get_notification_for_buyer_company(buyer_company):
    return get_notification(buyer_company)

def get_notification_for_seller_company(seller_company):
    return get_notification(seller_company)


def create_notification_for_buyer_employee(buyer_employee, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    create_notification(buyer_employee, notification_type, title, message, related_order, related_return, related_exchange)

def create_notification_for_seller_employee(seller_employee, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    create_notification(seller_employee, notification_type, title, message, related_order, related_return, related_exchange)

def create_notification_for_customer(customer, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    create_notification(customer, notification_type, title, message, related_order, related_return, related_exchange)

def create_notification_for_buyer_company(buyer_company, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    create_notification(buyer_company, notification_type, title, message, related_order, related_return, related_exchange)

def create_notification_for_seller_company(seller_company, notification_type, title, message, related_order=None, related_return=None, related_exchange=None):
    create_notification(seller_company, notification_type, title, message, related_order, related_return, related_exchange)


def get_user_type(user): 
    if hasattr(user, 'seller_employee_profile'):
        return CompanyTypes.SELLER.name
    elif hasattr(user, 'buyer_employee_profile'):
        return CompanyTypes.BUYER.name
    else: 
        return "CUSTOMER"