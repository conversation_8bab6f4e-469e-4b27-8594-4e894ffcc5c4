from django.contrib import admin
from .models import BlogPost, BlogCategory, FAQ, FAQCategory, Guide, Testimonial, BlogComments, HelpText, InformationText, VideoGuide


@admin.register(VideoGuide)
class VideoGuideAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'eng_title', 'shown', 'display_to_seller', 'display_to_buyer']
    list_filter = ['shown', 'display_to_seller', 'display_to_buyer']
    search_fields = ['eng_title', 'swe_title']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

@admin.register(InformationText)
class InformationTextAdmin(admin.ModelAdmin):
    list_display = ['id', 'uuid', 'vue_page', 'vue_english_header', 'shown']
    list_filter = ['shown', 'vue_page']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

@admin.register(HelpText)
class HelpTextAdmin(admin.ModelAdmin):
    list_display = ['id', 'uuid', 'model_name', 'model_field', 'eng', 'shown']
    list_filter = ['shown', 'model_name']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

@admin.register(BlogComments)
class BlogCommentsAdmin(admin.ModelAdmin):
    list_display = ['id', 'uuid', 'name', 'email', 'blog']
    readonly_fields = ['uuid', 'created_at', 'updated_at']
    
@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['id', 'uuid', 'name', 'title', 'company', 'shown']
    readonly_fields = ['uuid', 'created_at', 'updated_at']


@admin.register(Guide)
class GuideAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'swe_subject', 'eng_subject', 'display_to_seller', 'display_to_buyer']
    list_filter = ['display_to_seller', 'display_to_buyer']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'eng_question', 'shown', 'landing_page', 'display_to_seller', 'display_to_buyer']
    list_filter = ['shown', 'landing_page', 'display_to_seller', 'display_to_buyer']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

@admin.register(FAQCategory)
class FAQCategoryAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'eng_category', 'display_to_seller', 'display_to_buyer']
    list_filter = ['display_to_seller', 'display_to_buyer']
    readonly_fields = ['uuid', 'created_at', 'updated_at']
    
@admin.register(BlogCategory)
class BlogCategoryAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'eng_category']
    readonly_fields = ['uuid', 'created_at', 'updated_at']
    
@admin.register(BlogPost)
class BlogAdmin(admin.ModelAdmin): 
    list_display = ['id', 'uuid', 'eng_title', 'published_at', 'published', 'featured']
    list_filter = ['published', 'featured']
    readonly_fields = ['uuid', 'created_at', 'updated_at']

