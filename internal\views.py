from .models import <PERSON>logPost, BlogCategory, FAQ, FAQCategory, Guide, Testimonial, BlogComments, HelpText, InformationText, VideoGuide
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import viewsets, filters, mixins, status, generics
from .serializers import (
    FAQCategorySerializer, FAQSerializer, BlogCategorySerializer, 
    BlogSerializer, GuideSerializer, ContactFormSerializer, 
    TestimonialSerializer, TermsOfPurchaseSerializer,
    BlogCommentsSerializer, HelpTextSerializer, InformationTextSerializer,
    VideoGuideSerializer
)
from common.enums import CompanyTypes
from config.settings import BASE_DIR
from django.http import HttpResponse
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
import os
from django.template.loader import render_to_string
import logging 
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.exceptions import APIException
from common.utils import get_user_type



logger = logging.getLogger('billeasy_api')

class LandingPageFAQViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [AllowAny]
    serializer_class = FAQSerializer
    lookup_field="uuid"
    http_method_names = ['get']
    internal_endpoint = True
    
    
    def get_queryset(self): 
        return FAQ.objects.filter(shown=True, landing_page=True)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)


class BlogCommentsViewSet(viewsets.ModelViewSet): 
    permission_classes = [AllowAny]
    serializer_class = BlogCommentsSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['blog__eng_title', 'blog__swe_title']
    search_fields = ['name', 'comment', 'blog__eng_title', 'blog__swe_title']
    ordering_fields = ['name', 'created_at']
    lookup_field = "uuid"
    internal_endpoint = True
    

    def get_queryset(self): 
        return BlogComments.objects.filter(shown=True)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @swagger_auto_schema(auto_schema=None)
    def partial_update(self, request, uuid=None): 
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @swagger_auto_schema(auto_schema=None)
    def destroy(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    

class BlogCategoryViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [AllowAny]
    serializer_class = BlogCategorySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['swe_category', 'eng_category']
    search_fields = ['swe_category', 'eng_category']
    ordering_fields = ['swe_category', 'eng_category']
    internal_endpoint = True
    

    def get_queryset(self): 
        return BlogCategory.objects.all()

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)


class BlogViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [AllowAny]
    serializer_class = BlogSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['published', 'category__eng_category', 'category__swe_category', 'published_at']
    search_fields = ['swe_title', 'eng_title', 'swe_text', 'eng_text']
    ordering_fields = ['swe_title', 'eng_title', 'published_at']
    lookup_field = 'slug'
    internal_endpoint = True
    

    def get_queryset(self): 
        return BlogPost.objects.filter(published=True)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class FAQCategoryViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [IsAuthenticated]
    serializer_class = FAQCategorySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['swe_category', 'eng_category']
    search_fields = ['swe_category', 'eng_category']
    ordering_fields = ['swe_category', 'eng_category']
    lookup_field="uuid"
    internal_endpoint = True

    def get_queryset(self): 
        user_type = get_user_type(self.request.user) 
        if user_type == CompanyTypes.BUYER.name: 
            return FAQCategory.objects.filter(display_to_buyer=True)
        elif user_type == CompanyTypes.SELLER.name:
            return FAQCategory.objects.filter(display_to_seller=True)
        else: # CUSTOMER
            return FAQCategory.objects.none()

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)


class FAQViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [IsAuthenticated]
    serializer_class = FAQSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['category__eng_category', 'category__swe_category']
    search_fields = ['category__eng_category', 'category__eng_category', 'eng_question', 'swe_question', 'eng_answer', 'swe_answer']
    ordering_fields = []
    lookup_field="uuid"
    internal_endpoint = True

    def get_queryset(self): 
        user_type = get_user_type(self.request.user) 
        if user_type == CompanyTypes.BUYER.name:
            return FAQ.objects.filter(shown=True, landing_page=False, display_to_buyer=True)
        elif user_type == CompanyTypes.SELLER.name: 
            return FAQ.objects.filter(shown=True, landing_page=False, display_to_seller=True)
        else: 
            return FAQ.objects.none()

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    
class GuideViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [IsAuthenticated]
    serializer_class = GuideSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['eng_subject', 'swe_subject']
    search_fields = ['eng_subject', 'swe_subject', 'eng_text', 'swe_text']
    ordering_fields = []
    lookup_field="uuid"
    internal_endpoint = True

    def get_queryset(self):
        user_type = get_user_type(self.request.user) 
        if user_type == CompanyTypes.BUYER.name:
            return Guide.objects.filter(shown=True, display_to_buyer=True)
        elif user_type == CompanyTypes.SELLER.name:
            return Guide.objects.filter(shown=True, display_to_seller=True)
        else: 
            return Guide.objects.none()

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    
class LandingPageContactFormViewSet(mixins.CreateModelMixin, viewsets.GenericViewSet): 
    serializer_class = ContactFormSerializer
    permission_classes = [AllowAny]
    internal_endpoint = True
    

    def get_data(self, request) -> dict:
        return request.data if isinstance(request.data, dict) else request.data.dict()

    def create(self, request): 
        try:
            data = self.get_data(request)
            serializer = ContactFormSerializer(data=data)
            if serializer.is_valid():
                template = "email/admin-contact-form.html"
                context = {
                    "email": serializer.validated_data.get("email"),
                    "full_name": serializer.validated_data.get("full_name"),
                    "phone_number": serializer.validated_data.get("phone_number"),
                    "message": serializer.validated_data.get("message")
                }
                
                message = render_to_string(template, context)
                #TODO: send email
                # email_service = EmailSESService(
                #     is_admin=True,
                #     receivers=[AdministrationCredential.objects.last().reply_to_email_address],
                #     subject=f"Billeasy - Nytt meddelande",
                #     html_message=message,
                #     resource_type='contact_form'
                # )
                # sent, error = email_service.send_email_with_attachment()
                sent = True
                error = {}
                if not sent or error:
                    logger.error(f"Could not send email to admin about new landing page contact {error}")
                    raise APIException(error)
                return Response(
                    {
                        "swe": "Tack! Vi har mottagit ditt meddelande och återkommer snarast.",
                        "eng": "Thank you! We have received your message and will get back to you as soon as possible.",
                    },
                    status=status.HTTP_200_OK
                ) 
            else:
                raise DRFValidationError(serializer.errors)
        except Exception: 
            raise



class TestimonialViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [AllowAny]
    serializer_class = TestimonialSerializer
    lookup_field="uuid"
    http_method_names = ['get']
    internal_endpoint = True
    
    
    def get_queryset(self): 
        return Testimonial.objects.filter(shown=True)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    
    
class TermsOfPurchaseView(generics.ListAPIView): 
    serializer_class = TermsOfPurchaseSerializer
    permission_classes = [AllowAny]
    internal_endpoint = True

    def get(self, request, *args, **kwargs):
        terms_and_conditions =  os.path.join(
            BASE_DIR, 'static','pdf', 'TERMS_OF_PURCHASE.pdf'
        )
        
        # return as PDF
        with open(terms_and_conditions, mode='rb') as top:
            file_data = top.read()
        response = HttpResponse(
            file_data,
            content_type='application/pdf'
        )
        response['Content-Disposition'] = f'attachment; filename="kopvillkor.pdf"'
        return response
        

class HelpTextListView(viewsets.ReadOnlyModelViewSet):
    queryset = HelpText.objects.filter(shown=True)
    serializer_class = HelpTextSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['model_name', 'model_field']
    search_fields = ['model_name', 'model_field']
    ordering_fields = []
    internal_endpoint = True
    
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    
class InformationTextListView(viewsets.ReadOnlyModelViewSet):
    queryset = InformationText.objects.filter(shown=True)
    serializer_class = InformationTextSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter,filters.OrderingFilter]
    filterset_fields = ['vue_page','vue_english_header']
    search_fields = ['vue_page','vue_english_header']
    ordering_fields = []
    internal_endpoint = True
    
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)






class VideoGuideViewSet(viewsets.ReadOnlyModelViewSet): 
    permission_classes = [IsAuthenticated]
    serializer_class = VideoGuideSerializer
    filter_backends = [filters.SearchFilter,filters.OrderingFilter]
    search_fields = ['eng_title', 'swe_title', 'eng_description', 'swe_description']
    ordering_fields = ['eng_title', 'swe_title']
    internal_endpoint = True
    

    def get_queryset(self): 
        return VideoGuide.objects.filter(shown=True)

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)