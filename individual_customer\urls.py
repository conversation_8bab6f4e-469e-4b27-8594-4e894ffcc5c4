from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import CustomerAccountViewSet, CustomerOrderViewSet, CustomerNotificationViewSet, CustomerEventLogViewSet

router = DefaultRouter()
router.register(r'accounts', CustomerAccountViewSet)
router.register(r'orders', CustomerOrderViewSet, basename='customerorder')
router.register(r'notifications', CustomerNotificationViewSet, basename='customernotification')
router.register(r'event-logs', CustomerEventLogViewSet, basename='customereventlog')

urlpatterns = [
    path('', include(router.urls)),
]