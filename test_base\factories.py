import factory
from factory.django import DjangoModelFactory
from common.models import Company, User, Order, OrderItem, Permission, Role, EventLog, Notification, ProductReview, Country
from buyer.models import BuyerCompany, BuyerCompanyEmployee, BuyerCompanySetting, Address, BuyerSellerEmployeeAccess
from django.contrib.contenttypes.models import ContentType
from seller.models import (
    SellerCompany, SellerCompanyEmployee, SellerCompanySetting, ProductCategory,
    Product, ProductImage, ProductVariant, Discount, DiscountAssignment, DiscountCode,  
    PriceList, ProductPrice, Warehouse, WarehouseInventory,
    OrderCategory, Return, ReturnItem, Exchange, PackingSlip, DocumentCategory, Document,
    BuyerSellerRelationship, ProductVisibilityRule, VATCategory
)
from billing.models import Price, Plan, Addon, SubscriptionAddon, Subscription, PaymentPlans, PaymentTransaction
from individual_customer.models import CustomerAccount
from common.enums import (
    NotificationTypes, EventTypes, OrderStatuses, SubscriptionAddOns, 
    PaymentMethods, PaymentTransactionStatuses, PaymentTransactionTypes,
    DiscountTypes, VisibilityTypes
)
from common.utils import make_aware_datetime
from datetime import datetime, timedelta
import random
from faker.providers import BaseProvider
from faker import Faker

fake = Faker()


class AddressDict(factory.DictFactory):
    address = factory.Faker('street_address')
    city = factory.Faker('city')
    zip_code = factory.Faker('zipcode')
    country_code = factory.Faker('country_code')

class VATCategoryFactory(DjangoModelFactory):
    class Meta:
        model = VATCategory

    name = "VAT Category"

class CountryFactory(DjangoModelFactory):
    class Meta:
        model = Country

    name = "Sweden"
    code = "SE"

class CustomProvider(BaseProvider):
    def short_phone_number(self):
        return self.numerify(text='##########')[:20]

fake.add_provider(CustomProvider)

class CompanyFactory(DjangoModelFactory):
    class Meta:
        model = Company

    name = factory.Sequence(lambda n: f"Company {n}")
    organisation_number = factory.Sequence(lambda n: f"ORG{n}")
    street_address = factory.Faker('street_address')
    zip_code = factory.Faker('zipcode')
    city = factory.Faker('city')
    contact_phone_number = factory.LazyFunction(lambda: fake.short_phone_number())
    contact_email = factory.Faker('email')


class SellerCompanyFactory(DjangoModelFactory):
    class Meta:
        model = SellerCompany

    company = factory.SubFactory(CompanyFactory)
    webshop_is_public = True

class BuyerCompanyFactory(DjangoModelFactory):
    class Meta:
        model = BuyerCompany

    company = factory.SubFactory(CompanyFactory)
    credit_limit = factory.Faker('pydecimal', left_digits=5, right_digits=2, positive=True)

class UserFactory(DjangoModelFactory):
    class Meta:
        model = User

    email = factory.Sequence(lambda n: f"user{n}@example.com")
    password = factory.PostGenerationMethodCall('set_password', 'password')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')

class AddressFactory(DjangoModelFactory):
    class Meta:
        model = Address

    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    address_type = factory.Faker('random_element', elements=['SHIPPING', 'BILLING'])
    street_address = factory.Faker('street_address')
    city = factory.Faker('city')
    zip_code = factory.Faker('zipcode')
    country_code = factory.Faker('country_code')

class CustomerAccountFactory(DjangoModelFactory):
    class Meta:
        model = CustomerAccount
    
    user = factory.SubFactory(UserFactory)

class OrderFactory(DjangoModelFactory):
    class Meta:
        model = Order

    seller_company = factory.SubFactory(SellerCompanyFactory)
    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    order_number = factory.Sequence(lambda n: f"ORDER{n}")
    total_amount_to_pay = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    total_vat_amount = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    total_discount_amount = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    status = factory.Faker('random_element', elements=[status.value for status in OrderStatuses])
    shipping_address = factory.SubFactory(AddressDict)
    billing_address = factory.SubFactory(AddressDict)

    # Set up the GenericForeignKey
    content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(o.account))
    object_id = factory.SelfAttribute('account.id')
    account = factory.SubFactory('test_base.factories.BuyerCompanyEmployeeFactory')

    @factory.post_generation
    def ensure_json_fields(self, create, extracted, **kwargs):
        if not create:
            return
        
        # Ensure shipping_address and billing_address are JSON serialized
        self.shipping_address = {
            'address': self.shipping_address['address'],
            'city': self.shipping_address['city'],
            'zip_code': self.shipping_address['zip_code'],
            'country_code': self.shipping_address['country_code']
        }
        self.billing_address = {
            'address': self.billing_address['address'],
            'city': self.billing_address['city'],
            'zip_code': self.billing_address['zip_code'],
            'country_code': self.billing_address['country_code']
        }
        self.save()

class OrderItemFactory(DjangoModelFactory):
    class Meta:
        model = OrderItem

    order = factory.SubFactory(OrderFactory)
    description = factory.Faker('sentence')
    quantity = factory.Faker('random_int', min=1, max=10)
    price = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    vat_rate = factory.Faker('pydecimal', left_digits=2, right_digits=2, positive=True)

class PermissionFactory(DjangoModelFactory):
    class Meta:
        model = Permission

    name = factory.Sequence(lambda n: f"Permission {n}")
    codename = factory.Sequence(lambda n: f"permission_{n}")


class RoleFactory(DjangoModelFactory):
    class Meta:
        model = Role

    name = factory.Sequence(lambda n: f"Role {n}")
    company = factory.SubFactory(BuyerCompanyFactory)
    content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(o.company))
    object_id = factory.SelfAttribute('company.id')

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        company = kwargs.pop('company', None)
        if company is None:
            company = SellerCompanyFactory()  # Default to SellerCompany if not specified
        
        if isinstance(company, BuyerCompany):
            content_type = ContentType.objects.get_for_model(BuyerCompany)
        elif isinstance(company, SellerCompany):
            content_type = ContentType.objects.get_for_model(SellerCompany)
        else:
            raise ValueError("Company must be either BuyerCompany or SellerCompany")

        kwargs['content_type'] = content_type
        kwargs['object_id'] = company.id
        kwargs['company'] = company

        return super()._create(model_class, *args, **kwargs)

    @factory.post_generation
    def permissions(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for permission in extracted:
                self.permissions.add(permission)


class EventLogFactory(DjangoModelFactory):
    class Meta:
        model = EventLog

    user = factory.SubFactory(UserFactory)
    event_type = factory.Faker('random_element', elements=[choice[0] for choice in EventTypes.choices()])
    description = factory.Faker('sentence')
    ip_address = factory.Faker('ipv4')
    additional_data = factory.Dict({'key': 'value'})

    # Default to BuyerCompany as the content object, but this can be overridden
    content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(o.content_object))
    object_id = factory.SelfAttribute('content_object.id')
    content_object = factory.SubFactory(BuyerCompanyFactory)

    @factory.post_generation
    def set_content_object(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            self.content_type = ContentType.objects.get_for_model(extracted)
            self.object_id = extracted.id
            self.content_object = extracted

class NotificationFactory(DjangoModelFactory):
    class Meta:
        model = Notification

    title = factory.Faker('sentence')
    message = factory.Faker('paragraph')
    notification_type = factory.Faker('random_element', elements=[choice[0] for choice in NotificationTypes.choices()])
    is_read = False

    # Default to BuyerCompany as the recipient, but this can be overridden
    content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(o.recipient))
    object_id = factory.SelfAttribute('recipient.id')
    recipient = factory.SubFactory(BuyerCompanyFactory)

    @factory.post_generation
    def related_objects(self, create, extracted, **kwargs):
        if not create:
            return

        if 'related_order' in kwargs:
            self.related_order = kwargs['related_order']
        if 'related_return' in kwargs:
            self.related_return = kwargs['related_return']
        if 'related_exchange' in kwargs:
            self.related_exchange = kwargs['related_exchange']


class SellerCompanyEmployeeFactory(DjangoModelFactory):
    class Meta:
        model = SellerCompanyEmployee

    seller_company = factory.SubFactory(SellerCompanyFactory)
    user = factory.SubFactory(UserFactory)
    role = factory.SubFactory(RoleFactory, company=factory.SelfAttribute('..seller_company'))
    is_active = True

class SellerCompanySettingFactory(DjangoModelFactory):
    class Meta:
        model = SellerCompanySetting

    seller_company = factory.SubFactory(SellerCompanyFactory)
    reply_to_email_address = factory.Faker('email')

class ProductCategoryFactory(DjangoModelFactory):
    class Meta:
        model = ProductCategory

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Product Category {n}")

class ProductFactory(DjangoModelFactory):
    class Meta:
        model = Product

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Product {n}")
    description = factory.Faker('paragraph')
    base_price = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    vat_category = factory.SubFactory(VATCategoryFactory)

class ProductImageFactory(DjangoModelFactory):
    class Meta:
        model = ProductImage

    product = factory.SubFactory(ProductFactory)
    image = factory.django.ImageField()

class ProductVariantFactory(DjangoModelFactory):
    class Meta:
        model = ProductVariant

    product = factory.SubFactory(ProductFactory)
    name = factory.Sequence(lambda n: f"Variant {n}")
    sku = factory.Sequence(lambda n: f"SKU{n}")
    vat_category = factory.SubFactory(VATCategoryFactory)

class DiscountFactory(DjangoModelFactory):
    class Meta:
        model = Discount

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Discount {n}")
    description = factory.Faker('paragraph', nb_sentences=3)
    discount_type = DiscountTypes.AMOUNT.name
    value = factory.Faker('pydecimal', left_digits=2, right_digits=2, positive=True)
    start_date = make_aware_datetime(datetime.now() - timedelta(days=random.randint(1, 10)))
    end_date = make_aware_datetime(datetime.now() + timedelta(days=random.randint(1, 10)))
    is_active = True
    can_be_combined = factory.Faker('boolean')

class DiscountAssignmentFactory(DjangoModelFactory):
    class Meta:
        model = DiscountAssignment

    discount = factory.SubFactory(DiscountFactory)
    content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(o.content_object))
    object_id = factory.SelfAttribute('content_object.id')
    content_object = factory.SubFactory(ProductFactory)

class DiscountCodeFactory(DjangoModelFactory):
    class Meta:
        model = DiscountCode

    discount = factory.SubFactory(DiscountFactory)
    code = factory.Sequence(lambda n: f"CODE{n}")
    max_uses = factory.Faker('random_int', min=1, max=100)
    times_used = 0

class PriceListFactory(DjangoModelFactory):
    class Meta:
        model = PriceList

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Price List {n}")

class ProductPriceFactory(DjangoModelFactory):
    class Meta:
        model = ProductPrice

    price_list = factory.SubFactory(PriceListFactory)
    product = factory.SubFactory(ProductFactory)
    price = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)

class WarehouseFactory(DjangoModelFactory):
    class Meta:
        model = Warehouse

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Warehouse {n}")
    address = factory.Faker('address')
    country = factory.SubFactory(CountryFactory)

class WarehouseInventoryFactory(DjangoModelFactory):
    class Meta:
        model = WarehouseInventory

    warehouse = factory.SubFactory(WarehouseFactory)
    product_variant = factory.SubFactory(ProductVariantFactory)
    quantity = factory.Faker('random_int', min=0, max=1000)

class OrderCategoryFactory(DjangoModelFactory):
    class Meta:
        model = OrderCategory

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Order Category {n}")

class ReturnFactory(DjangoModelFactory):
    class Meta:
        model = Return

    order = factory.SubFactory(OrderFactory)

class ReturnItemFactory(DjangoModelFactory):
    class Meta:
        model = ReturnItem

    return_request = factory.SubFactory(ReturnFactory)
    order_item = factory.SubFactory(OrderItemFactory)
    quantity = factory.Faker('random_int', min=1, max=10)

class ExchangeFactory(DjangoModelFactory):
    class Meta:
        model = Exchange

    original_order = factory.SubFactory(OrderFactory)
    original_order_item = factory.SubFactory(OrderItemFactory)
    new_product = factory.SubFactory(ProductFactory)
    new_product_variant = factory.SubFactory(ProductVariantFactory)

class PackingSlipFactory(DjangoModelFactory):
    class Meta:
        model = PackingSlip

    order = factory.SubFactory(OrderFactory)
    packing_slip_number = factory.Sequence(lambda n: f"PACK{n}")
    packed_by = factory.SubFactory(SellerCompanyEmployeeFactory)
    packed_at = factory.LazyFunction(lambda: make_aware_datetime(factory.Faker('date_this_year', before_today=True).evaluate(None, None, {'locale': None})))

class DocumentCategoryFactory(DjangoModelFactory):
    class Meta:
        model = DocumentCategory

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Document Category {n}")

class DocumentFactory(DjangoModelFactory):
    class Meta:
        model = Document

    seller_company = factory.SubFactory(SellerCompanyFactory)
    name = factory.Sequence(lambda n: f"Document {n}")
    file = factory.django.FileField()
    uploaded_by = factory.SubFactory(SellerCompanyEmployeeFactory)
    category = factory.SubFactory(DocumentCategoryFactory)



class BuyerCompanyEmployeeFactory(DjangoModelFactory):
    class Meta:
        model = BuyerCompanyEmployee

    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    user = factory.SubFactory(UserFactory)
    is_active = True

    @factory.post_generation
    def role(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            self.role = extracted
        else:
            self.role = RoleFactory(company=self.buyer_company)
class BuyerCompanySettingFactory(DjangoModelFactory):
    class Meta:
        model = BuyerCompanySetting

    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    notification_preferences = factory.Dict({'email': True, 'sms': False})
    contact_name = factory.Faker('name')
    contact_email = factory.Faker('email')
    contact_phone = factory.LazyFunction(lambda: fake.short_phone_number())



class BuyerSellerEmployeeAccessFactory(DjangoModelFactory):
    class Meta:
        model = BuyerSellerEmployeeAccess

    seller_company = factory.SubFactory(SellerCompanyFactory)
    buyer_employee = factory.SubFactory(BuyerCompanyEmployeeFactory)


class BuyerSellerRelationshipFactory(DjangoModelFactory): 
    class Meta: 
        model = BuyerSellerRelationship

    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    seller_company = factory.SubFactory(SellerCompanyFactory)
    is_active = True
    customer_number = factory.Sequence(lambda n: f"CUST{n}")
    notes = factory.Faker('paragraph')
    price_list = factory.SubFactory(PriceListFactory)


class PriceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Price

    description = factory.Faker('sentence')
    quarterly_price = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    yearly_price = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    stripe_product_id = factory.Faker('uuid4')
    stripe_price_id_quarterly = factory.Faker('uuid4')
    stripe_price_id_yearly = factory.Faker('uuid4')
    accounting_product_id = factory.Faker('uuid4')
    quarterly_stripe_payment_link = factory.Faker('url')
    yearly_stripe_payment_link = factory.Faker('url')

class PlanFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Plan

    name = factory.Faker('word')
    description = factory.Faker('paragraph')
    features = factory.Dict({'feature1': True, 'feature2': False})
    price = factory.SubFactory(PriceFactory)
    included_user_accounts = factory.Faker('random_int', min=1, max=10)
    included_products = factory.Faker('random_int', min=10, max=100)
    included_customers = factory.Faker('random_int', min=10, max=100)
    modules_included = factory.Dict({'module1': True, 'module2': False})
    level = factory.Faker('random_int', min=1, max=5)

class AddonFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Addon

    name = factory.Faker('word')
    description = factory.Faker('paragraph')
    price = factory.SubFactory(PriceFactory)
    addon_type = factory.Iterator([choice[0] for choice in SubscriptionAddOns.choices()])

class SubscriptionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Subscription

    company = factory.SubFactory(CompanyFactory)
    plan = factory.SubFactory(PlanFactory)
    payment_plan = factory.Iterator([choice[0] for choice in PaymentPlans.choices()])
    payment_method = factory.Iterator([choice[0] for choice in PaymentMethods.choices()])
    start_date = factory.Faker('date_object')
    end_date = factory.LazyAttribute(lambda o: o.start_date + timedelta(days=365 if o.payment_plan == PaymentPlans.YEARLY.name else 90))
    current_period_start_date = factory.SelfAttribute('start_date')
    current_period_end_date = factory.SelfAttribute('end_date')
    cost = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    is_active = True
    customer_number = factory.Faker('uuid4')
    stripe_customer_id = factory.Faker('uuid4')
    stripe_subscription_id = factory.Faker('uuid4')
    accounting_contract_id = factory.Faker('uuid4')
    billing_email = factory.Faker('email')
    stripe_payment_methods = factory.Dict({'method1': 'card', 'method2': 'bank'})
    remind_after_x_days = factory.Faker('random_int', min=1, max=10)
    send_payment_reminders = True
    inactivate_account_x_days_after_reminder = factory.Faker('random_int', min=1, max=30)

class SubscriptionAddonFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SubscriptionAddon

    subscription = factory.SubFactory(SubscriptionFactory)
    addon = factory.SubFactory(AddonFactory)
    quantity = factory.Faker('random_int', min=1, max=5)

class PaymentTransactionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = PaymentTransaction

    subscription = factory.SubFactory(SubscriptionFactory)
    amount = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    payment_date = factory.Faker('date_object')
    status = factory.Iterator([choice[0] for choice in PaymentTransactionStatuses.choices()])
    transaction_type = factory.Iterator([choice[0] for choice in PaymentTransactionTypes.choices()])
    due_date = factory.Faker('future_date')
    amount_due = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    final_payment_date = factory.Faker('future_date')
    invoice_number = factory.Faker('uuid4')
    stripe_id = factory.Faker('uuid4')

class ProductReviewFactory(factory.django.DjangoModelFactory):
    seller_company = factory.SubFactory(SellerCompanyFactory)
    buyer = factory.SubFactory(BuyerCompanyEmployeeFactory)
    rating = factory.Faker('random_int', min=1, max=5)
    comment = factory.Faker('paragraph')
    is_verified_purchase = factory.Faker('boolean')

    class Meta:
        model = ProductReview

class ProductVisibilityRuleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ProductVisibilityRule

    seller_company = factory.SubFactory(SellerCompanyFactory)
    buyer_company = factory.SubFactory(BuyerCompanyFactory)
    product = factory.SubFactory(ProductFactory)
    visibility_type = factory.Iterator([choice[0] for choice in VisibilityTypes.choices()])