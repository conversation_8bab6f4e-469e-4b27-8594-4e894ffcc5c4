from django.db import models
from django.contrib.contenttypes.models import ContentType
from common.enums import EventTypes

class EventLogMixin(models.Model):
    """
    Mixin for logging events in models. Have models inherit from this mixing to automatically log events.
    """
    

    class Meta:
        abstract = True

    def log_event(self, event_type, user=None, description=None, additional_data=None):
        from .models import EventLog  # Import here to avoid circular imports

        if isinstance(self, EventLog):
            return  # Prevent infinite recursion
        EventLog.objects.create(
            user=user,
            event_type=event_type,
            content_type=ContentType.objects.get_for_model(self),
            object_id=self.id,
            description=description or f"{event_type} event on {self.__class__.__name__}",
            additional_data=additional_data
        )

    def save(self, *args, **kwargs):
        is_new = self._state.adding
        super().save(*args, **kwargs)
        
        if is_new:
            self.log_event(EventTypes.CREATE.name)
        else:
            self.log_event(EventTypes.UPDATE.name)

    def delete(self, *args, **kwargs):
        self.log_event(EventTypes.DELETE.name)
        super().delete(*args, **kwargs)


class APIEventLogMixin:
    """
    Mixin for logging events in API views. Have views inherit from this mixing to automatically log events.
    """
    

    event_type = None
    log_description = None

    def get_event_type(self):
        if self.event_type is None:
            raise NotImplementedError("Define event_type in your view")
        return self.event_type

    def get_log_description(self):
        return self.log_description or f"{self.get_event_type()} event in {self.__class__.__name__}"

    def get_additional_data(self):
        return None

    def log_event(self, request):
        from .models import EventLog
        if isinstance(self, EventLog):
            return  # Prevent infinite recursion
        EventLog.objects.create(
            user=request.user if request.user.is_authenticated else None,
            event_type=self.get_event_type(),
            content_type=self.get_content_type(),
            object_id=self.get_object_id(),
            description=self.get_log_description(),
            ip_address=request.META.get('REMOTE_ADDR'),
            additional_data=self.get_additional_data()
        )

    def get_content_type(self):
        return None

    def get_object_id(self):
        return None

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)
        self.log_event(request)



class CompanyRoleSerializerMixin:
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['company'] = instance.company.id
        return representation