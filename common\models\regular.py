from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import validate_email
from common.managers import CustomUserManager
import re
from django.db.models import Max
from model_utils import FieldTracker
from common.constants.countries import ALL_COUNTRIES
from .abstract import BaseModel, Account

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from common.enums import CompanyTypes, EventTypes, OrderStatuses, ExchangeStatuses, NotificationTypes
from decimal import Decimal
from django.utils.timezone import make_aware
import pytz
from datetime import datetime
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError



optional = {
    "null": True,
    "blank": True
}

def default_company_types():
    return [ct.name for ct in CompanyTypes]

def clean_special_characters(name):
    chars_to_replace = {
        "Å": "A", "Ä": "A", "Ö": "O",
        "å": "a", "ä": "a", "ö": "o"
    }
    name = (''.join(chars_to_replace.get(s, s) for s in name))
    return re.sub('[^a-zA-Z0-9 \n.]', '', name).replace(" ", "_").strip().lower()

def logo_directory_path(instance, filename):
    return f'users/logos/{instance.uuid}/{filename}'

def profile_picture_directory_path(instance, filename):
    return f'users/profile_pictures/{instance.uuid}/{filename}'

class Company(BaseModel):
    """
    Represents a company in the system, which can be either a buyer or a seller.
    """
    name = models.CharField(max_length=150, help_text="Company name")
    organisation_number = models.CharField(max_length=25, help_text="Official organization number")
    identifier = models.CharField(max_length=255, **optional, help_text="Unique identifier for the company")
    vat_number = models.CharField(max_length=255, **optional, help_text="VAT number of the company")
    street_address = models.CharField(max_length=255, help_text="Street address of the company")
    zip_code = models.CharField(max_length=255, help_text="ZIP code of the company")
    city = models.CharField(max_length=255, help_text="City where the company is located")
    contact_phone_number = models.CharField(max_length=20, help_text="Primary contact phone number", **optional)
    contact_email = models.EmailField(validators=[validate_email], help_text="Primary contact email address")
    country_code = models.CharField(
        max_length=2,
        default="SE",
        choices=[(cnt["alpha-2"], cnt["name"]) for cnt in ALL_COUNTRIES],
        help_text="Two-letter country code"
    )
    logo = models.ImageField(upload_to=logo_directory_path, **optional, help_text="Company logo")
    is_active = models.BooleanField(default=True, help_text="If false, company account is inactive")

    tracker = FieldTracker()
    

    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.identifier or self.tracker.has_changed('name'):
            self.identifier = clean_special_characters(self.name)
        super().save(*args, **kwargs)

    def get_company_type(self):
        from buyer.models import BuyerCompany
        from seller.models import SellerCompany
        if BuyerCompany.objects.filter(company=self).exists():
            return CompanyTypes.BUYER.name
        elif SellerCompany.objects.filter(company=self).exists():
            return CompanyTypes.SELLER.name
        else:
            return None
        
    def get_company_type_instance(self):
        from buyer.models import BuyerCompany
        from seller.models import SellerCompany
        if self.get_company_type() == CompanyTypes.BUYER.name:
            return BuyerCompany.objects.get(company=self)
        elif self.get_company_type() == CompanyTypes.SELLER.name:
            return SellerCompany.objects.get(company=self)
        else:
            return None
        
    def get_company_subscription(self):
        from billing.models import Subscription
        if self.get_company_type() == CompanyTypes.BUYER.name:
            return None
        elif self.get_company_type() == CompanyTypes.SELLER.name:
            return Subscription.objects.get(company=self)
        else:
            return None
    
    class Meta:
        verbose_name_plural = "Companies"
        ordering = ['-id']

class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    Custom user model for the system.
    """
    email = models.EmailField(unique=True, validators=[validate_email], help_text="User's email address (used for login)")
    first_name = models.CharField(max_length=255, **optional, help_text="User's first name")
    last_name = models.CharField(max_length=255, **optional, help_text="User's last name")
    full_name = models.CharField(max_length=255, **optional, help_text="User's full name")
    phone = models.CharField(max_length=255, **optional, help_text="User's phone number")
    is_staff = models.BooleanField(default=False, help_text="Designates whether the user can log into the admin site")
    is_active = models.BooleanField(default=True, help_text="Designates whether this user account should be considered active")
    is_verified = models.BooleanField(default=False, help_text="Will be True after the user has set their password through the welcome email")
    profile_picture = models.ImageField(upload_to=profile_picture_directory_path, **optional, help_text="User's profile picture")
    last_login = models.DateTimeField(**optional, help_text="Date and time of the user's last login")

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    objects = CustomUserManager()
    
    def __str__(self):
        if self.email and self.first_name:
            return f"{self.email} - {self.first_name}"
        elif self.email:
            return self.email
        elif self.first_name:
            return self.first_name
        else:
            return str(self.uuid)

    def save(self, *args, **kwargs):
        if not self.full_name:
            self.full_name = f"{self.first_name or ''} {self.last_name or ''}".strip()
        super().save(*args, **kwargs)

    def get_full_name(self):
        return f"{self.first_name or ''} {self.last_name or ''}".strip()

    class Meta: 
        ordering = ['-id']





class Permission(BaseModel):
    """
    Represents a single permission in the system.
    """
    name = models.CharField(max_length=255, unique=True)
    codename = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    company_types = models.JSONField(
        default=default_company_types,
        help_text="List of company types this permission can be applied to"
    )

    def __str__(self):
        return self.name

    def can_be_used_by(self, company_type):
        return company_type in self.company_types
    
    class Meta: 
        ordering = ['-id']

class Role(BaseModel):
    """
    Represents a role that can be assigned to employees.
    """
    name = models.CharField(max_length=255)
    permissions = models.ManyToManyField(Permission, related_name='roles')
    
    # Generic foreign key to link to either BuyerCompany or SellerCompany
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    company = GenericForeignKey('content_type', 'object_id')

    class Meta:
        unique_together = ['name', 'content_type', 'object_id']
        ordering = ['-id']

    def __str__(self):
        return f"{self.name} - {self.company}"
    


class EventLog(BaseModel):

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    event_type = models.CharField(max_length=10, choices=EventTypes.choices())
    
    # This allows us to link to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # For storing additional data as JSON
    additional_data = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"{self.event_type} - {self.content_type} - {self.user}"

    class Meta:
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
        ]
        ordering = ['-id']




class Order(BaseModel):
    seller_company = models.ForeignKey('seller.SellerCompany', on_delete=models.CASCADE, related_name='orders')
    buyer_company = models.ForeignKey('buyer.BuyerCompany', on_delete=models.CASCADE, related_name='orders', null=True, blank=True)
    
    # This can be used for individual customers or buyer employees
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, related_name='orders')
    object_id = models.PositiveIntegerField()
    account = GenericForeignKey('content_type', 'object_id')

    order_number = models.CharField(max_length=50, unique=True)
    total_amount_to_pay = models.DecimalField(max_digits=10, decimal_places=2)
    total_vat_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=OrderStatuses.choices(), default=OrderStatuses.PENDING.value)
    prices_includes_vat = models.BooleanField(default=True)
    
    is_split_order = models.BooleanField(default=False, help_text="If true, the order was split into multiple orders due to shipped from multiple warehouses")
    placed_at = models.DateTimeField(auto_now_add=True)

    discount_code = models.ForeignKey('seller.DiscountCode', on_delete=models.SET_NULL, related_name='orders', **optional)
    shipping_address = models.JSONField(default=dict)
    billing_address = models.JSONField(default=dict)
    
    # Seller-specific fields. NOTE: make sure to exclude seller_notes from the BuyerOrderDetailSerializer
    category = models.ForeignKey('seller.OrderCategory', on_delete=models.SET_NULL, related_name='orders', **optional)
    manual = models.BooleanField(default=False, help_text="If true, the order was created manually by a seller employee")
    created_by = models.ForeignKey('seller.SellerCompanyEmployee', on_delete=models.SET_NULL, related_name='created_orders', **optional)

    def __str__(self):
        return f"Order {self.order_number} by {self.account}"

    class Meta: 
        ordering = ['-id']

    def save(self, *args, **kwargs):
        if not self.pk:
            from buyer.models import BuyerCompanyEmployee
            account_instance = self.account
            if isinstance(account_instance, BuyerCompanyEmployee):
                if not self.buyer_company:
                    self.buyer_company = account_instance.buyer_company
            
            if not self.order_number:
                self.order_number = self.generate_order_number()

        self.total_amount_to_pay = Decimal('0.00')
        self.total_vat_amount = Decimal('0.00')
        self.total_discount_amount = Decimal('0.00')


        super().save(*args, **kwargs)
        for item in self.items.all():
            self.total_amount_to_pay += item.total_amount_after_discount
            self.total_vat_amount += item.total_vat
            self.total_discount_amount += item.discount_amount

    def extract_number(self, order_number):
        match = re.search(r'\d+', order_number)
        return int(match.group()) if match else 0

    def generate_order_number(self):
        # Fetch seller company settings
        seller_settings = self.seller_company.settings
        
        # Generate the new order number, specific to the seller_company
        # Get all orders for this seller company
        orders = Order.objects.filter(seller_company=self.seller_company)
       
        # Find the highest order number
        highest_order = max(orders, key=lambda o: self.extract_number(o.order_number), default=None)
        
        if highest_order is None:
            # If no previous orders, start with 1
            new_number = 1
        else:
            # Extract the numeric part from the highest order number
            match = re.search(r'\d+', highest_order.order_number)
            if match:
                new_number = int(match.group()) + 1
            else:
                # Fallback if no number found
                new_number = 1
        
        # Apply prefix and suffix from settings
        prefix = seller_settings.order_number_prefix or ""
        suffix = seller_settings.order_number_suffix or ""
       
        return f"{prefix}{new_number:04d}{suffix}"

    def update_status(self, new_status):
        if new_status in OrderStatuses.values() or new_status in OrderStatuses.keys():
            self.status = new_status
            self.save()
            OrderStatusHistory.objects.create(order=self, status=new_status)
        else:
            raise ValueError("Invalid status")

    def get_status_history(self):
        return self.status_history.all().order_by('-timestamp')

    def is_editable(self):
        return self.status in [OrderStatuses.PENDING.name, OrderStatuses.PROCESSING.name]

    def get_total_shipped_quantity(self):
        return sum(item.shipped_quantity for item in self.items.all())
    
    def is_fully_shipped(self):
        return all(item.is_fully_shipped() for item in self.items.all())

    def custom_get_status_display(self):
        if self.is_fully_shipped():
            return "Fully Shipped"
        elif any(item.shipped_quantity > 0 for item in self.items.all()):
            return "Partially Shipped"
        else:
            return self.get_status_display()

    def get_category_name(self):
        return self.category.name if self.category else "Uncategorized"

    def get_category_color(self):
        return self.category.color if self.category else "#FFFFFF"
    
    def get_return_status(self):
        returns = self.returns.all()
        if not returns:
            return None
        
        statuses = set(r.status for r in returns)
        if OrderStatuses.REFUNDED.value in statuses:
            return OrderStatuses.REFUNDED.value
        elif OrderStatuses.RECEIVED.value in statuses:
            return OrderStatuses.RECEIVED.value
        elif OrderStatuses.APPROVED.value in statuses:
            return OrderStatuses.APPROVED.value
        elif OrderStatuses.REQUESTED.value in statuses:
            return OrderStatuses.REQUESTED.value
        else:
            return OrderStatuses.REJECTED.value

    def get_exchange_status(self):
        exchanges = self.exchanges.all()
        if not exchanges:
            return None
        
        statuses = set(e.status for e in exchanges)
        if ExchangeStatuses.COMPLETED.value in statuses:
            return OrderStatuses.COMPLETED.value
        elif ExchangeStatuses.SHIPPED.value in statuses:
            return ExchangeStatuses.SHIPPED.value
        elif ExchangeStatuses.APPROVED.value in statuses:
            return ExchangeStatuses.APPROVED.value
        elif ExchangeStatuses.REQUESTED.value in statuses:
            return ExchangeStatuses.REQUESTED.value
        else:
            return ExchangeStatuses.REJECTED.value

    def get_return_exchange_summary(self):
        return_status = self.get_return_status()
        exchange_status = self.get_exchange_status()
        
        if return_status and exchange_status:
            return f"Return: {return_status}, Exchange: {exchange_status}"
        elif return_status:
            return f"Return: {return_status}"
        elif exchange_status:
            return f"Exchange: {exchange_status}"
        else:
            return "No returns or exchanges"

class OrderItem(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')

    # Seller specific fields. NOTE: make sure to exlcude these fields in the buyer order serializer
    product = models.ForeignKey('seller.Product', on_delete=models.SET_NULL, null=True, related_name='order_items')
    product_variant = models.ForeignKey('seller.ProductVariant', on_delete=models.SET_NULL, null=True, blank=True, related_name='order_items')
    
    description = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    vat_rate = models.DecimalField(max_digits=5, decimal_places=2)
    total_vat = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount_after_discount = models.DecimalField(max_digits=10, decimal_places=2)

    warehouse = models.ForeignKey('seller.Warehouse', on_delete=models.SET_NULL, **optional)
    
    expected_delivery_date = models.DateField(null=True, blank=True)
    shipped_quantity = models.PositiveIntegerField(default=0)
    shipped_at = models.DateTimeField(null=True, blank=True)

    class Meta: 
        ordering = ['-id']

    def save(self, *args, **kwargs):
        # Calculate the price if it's not set
        if not self.price:
            customer = self.order.buyer_company or self.order.account
            discount_code = getattr(self.order, 'discount_code', None)
            
            if self.product_variant:
                self.price, self.discount_percentage = self.product_variant.get_price_with_discount(
                    customer, quantity=self.quantity, discount_code=discount_code)
                self.vat_rate = self.product_variant.get_vat_rate(self.order.shipping_address['country_code'])
                self.description = self.product_variant.name
            else:
                self.price, self.discount_percentage = self.product.get_price_with_discount(
                    customer, quantity=self.quantity, discount_code=discount_code)

                self.vat_rate = self.product.get_vat_rate(self.order.shipping_address['country_code'])
                self.description = self.product.name


        # The price we have now is the discounted price
        discounted_unit_price = self.price

        # Calculate amounts based on the discounted price
        if self.order.prices_includes_vat:
            vat_multiplier = Decimal('1.00') + (self.vat_rate / Decimal('100.00'))
            ex_vat_unit_price = discounted_unit_price / vat_multiplier
            self.total_vat = (discounted_unit_price - ex_vat_unit_price) * self.quantity
        else:
            ex_vat_unit_price = discounted_unit_price
            self.total_vat = (ex_vat_unit_price * self.vat_rate / Decimal('100.00')) * self.quantity

        # Calculate total amounts
        self.total_amount_after_discount = discounted_unit_price * self.quantity
        
        # Calculate the pre-discount amount
        if self.discount_percentage:
            pre_discount_unit_price = discounted_unit_price / (Decimal('1.00') - (self.discount_percentage / Decimal('100.00')))
        else:
            pre_discount_unit_price = discounted_unit_price
        
        self.total_amount = pre_discount_unit_price * self.quantity
        
        # Calculate the discount amount
        self.discount_amount = self.total_amount - self.total_amount_after_discount

        if not self.order.prices_includes_vat:
            self.total_amount_after_discount += self.total_vat

        super().save(*args, **kwargs)
        self.order.save()

    def is_fully_shipped(self):
        return self.shipped_quantity == self.quantity

    def ship(self, quantity, warehouse):
        from common.utils import make_aware_datetime

        if quantity <= (self.quantity - self.shipped_quantity):
            self.shipped_quantity += quantity
            self.warehouse = warehouse
            if self.shipped_quantity == self.quantity:
                self.shipped_at = make_aware_datetime(datetime.now())
            self.save()
        else:
            raise ValueError("Shipped quantity cannot exceed remaining quantity")


class OrderStatusHistory(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_history')
    status = models.CharField(max_length=20, choices=OrderStatuses.choices())
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.order.order_number} - {self.custom_get_status_display()} at {self.timestamp}"
    
    class Meta: 
        ordering = ['-id']





class Notification(BaseModel):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    recipient = GenericForeignKey('content_type', 'object_id')

    notification_type = models.CharField(max_length=100, choices=NotificationTypes.choices())
    title = models.CharField(max_length=255)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    # Optional: Link to related objects
    related_order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True)
    related_return = models.ForeignKey('seller.Return', on_delete=models.SET_NULL, null=True, blank=True)
    related_exchange = models.ForeignKey('seller.Exchange', on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        ordering = ['-id']

    def __str__(self):
        return f"{self.get_notification_type_display()} for {self.recipient}"

    def mark_as_read(self):
        self.is_read = True
        self.read_at = make_aware(datetime.now(), timezone=pytz.timezone('Europe/Stockholm'))
        self.save()


class Country(BaseModel):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=2, unique=True)

    def __str__(self):
        return self.name

    



class ProductReview(BaseModel):
    seller_company = models.ForeignKey('seller.SellerCompany', on_delete=models.CASCADE, related_name='product_reviews')
    product = models.ForeignKey('seller.Product', on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    product_variant = models.ForeignKey('seller.ProductVariant', on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    buyer = models.ForeignKey('buyer.BuyerCompanyEmployee', on_delete=models.CASCADE, related_name='reviews')
    rating = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField()
    is_verified_purchase = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=(
                    models.Q(product__isnull=False, product_variant__isnull=True) |
                    models.Q(product__isnull=True, product_variant__isnull=False)
                ),
                name='product_xor_product_variant'
            )
        ]
        ordering = ['-id']

    def __str__(self):
        reviewed_item = self.product or self.product_variant
        return f"Review for {reviewed_item} by {self.buyer}"

    def clean(self):
        if (self.product and self.product_variant) or (not self.product and not self.product_variant):
            raise ValidationError({"eng": "Specify either a product or a product variant, not both or neither.", "swe": "Ange antingen en produkt eller en produktvariant, inte båda eller ingen."})

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)