from rest_framework.routers import DefaultRouter
from django.urls import path, include
from .views import PlanViewSet, PaymentTransactionViewSet, AddonViewSet, SubscriptionViewSet, SignupView

router = DefaultRouter()
router.register(r'plans', PlanViewSet)
router.register(r'payments', PaymentTransactionViewSet, basename='payment')
router.register(r'addons', AddonViewSet)
router.register(r'subscriptions', SubscriptionViewSet, basename='subscription')

urlpatterns = [
    path('', include(router.urls)),
    path('signup/', SignupView.as_view(), name='signup'),
]