# In seller/services.py

from django.db.models import Sum, F
from .models import Warehouse, WarehouseInventory
from common.enums import WarehouseStrategyOptions

class WarehouseService:
    @staticmethod
    def get_warehouses_for_order(order, seller_settings):
        strategy = seller_settings.warehouse_strategy
        warehouses = Warehouse.objects.filter(seller_company=order.seller_company)

        if strategy == WarehouseStrategyOptions.NEAREST.name:
            return WarehouseService._get_nearest_warehouses(warehouses, order.shipping_address)
        elif strategy == WarehouseStrategyOptions.MOST_STOCK.name:
            return WarehouseService._get_most_stock_warehouses(warehouses, order.items.all())
        elif strategy == WarehouseStrategyOptions.PRIORITY.name:
            return WarehouseService._get_priority_warehouses(warehouses)
        
        return warehouses

    @staticmethod
    def _get_nearest_warehouses(warehouses, shipping_address):
        country_code = shipping_address['country_code']
        city = shipping_address['city']
        return warehouses.order_by(
            F('country__code').desc() == country_code,
            F('city').desc() == city,
        )

    @staticmethod
    def _get_most_stock_warehouses(warehouses, order_items):
        warehouse_stock = {}
        for warehouse in warehouses:
            stock_sum = WarehouseInventory.objects.filter(
                warehouse=warehouse,
                product_variant__in=[item.product_variant for item in order_items if item.product_variant]
            ).aggregate(total_stock=Sum('quantity'))['total_stock'] or 0
            warehouse_stock[warehouse.id] = stock_sum
        
        return sorted(warehouses, key=lambda w: warehouse_stock.get(w.id, 0), reverse=True)

    @staticmethod
    def _get_priority_warehouses(warehouses):
        return warehouses.order_by('priority')

    @staticmethod
    def allocate_order_items(order_data, warehouses):
        allocated_items = []
        for item_data in order_data['items']:
            product = item_data['product']
            variant = item_data.get('product_variant')
            quantity_to_allocate = item_data['quantity']
            
            for warehouse in warehouses:
                inventory = WarehouseInventory.objects.filter(
                    warehouse=warehouse,
                    product_variant=variant or product
                ).first()
                
                if inventory and inventory.quantity > 0:
                    allocated_quantity = min(quantity_to_allocate, inventory.quantity)
                    allocated_items.append({
                        'product': product,
                        'product_variant': variant,
                        'warehouse': warehouse,
                        'quantity': allocated_quantity
                    })
                    quantity_to_allocate -= allocated_quantity
                    
                    if quantity_to_allocate == 0:
                        break
            
            if quantity_to_allocate > 0:
                raise ValueError(f"Insufficient stock for product {product.name}")
        
        return allocated_items

    @staticmethod
    def deduct_inventory(allocated_items):
        for item in allocated_items:
            inventory = WarehouseInventory.objects.get(
                warehouse=item['warehouse'],
                product_variant=item['product_variant'] or item['product']
            )
            inventory.quantity -= item['quantity']
            inventory.save()